﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Account" xml:space="preserve">
    <value>Conto</value>
  </data>
  <data name="Account Data" xml:space="preserve">
    <value>Dati del conto</value>
  </data>
  <data name="Account Settings" xml:space="preserve">
    <value>Impostazioni dell’account</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Azioni</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Attivo</value>
  </data>
  <data name="Activites setup" xml:space="preserve">
    <value>Modificare le impostazioni dell’attività</value>
  </data>
  <data name="Activities List" xml:space="preserve">
    <value>Richieste</value>
  </data>
  <data name="Activity" xml:space="preserve">
    <value>Richieste </value>
  </data>
  <data name="ActivityDetails" xml:space="preserve">
    <value>Dettagli</value>
  </data>
  <data name="ActivityDetailsTitle" xml:space="preserve">
    <value>La richiesta selezionata</value>
  </data>
  <data name="Add to archive" xml:space="preserve">
    <value>Archiviare</value>
  </data>
  <data name="AddAmount" xml:space="preserve">
    <value>Aggiungere importo</value>
  </data>
  <data name="AdditionalServices" xml:space="preserve">
    <value>Servizi aggiuntivi</value>
  </data>
  <data name="Additive" xml:space="preserve">
    <value>Aggiunta</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Indirizzo</value>
  </data>
  <data name="Administrator" xml:space="preserve">
    <value>Amministratore</value>
  </data>
  <data name="AdvertisingList" xml:space="preserve">
    <value>Elenco settoriale a fini pubblicitari</value>
  </data>
  <data name="Albanian" xml:space="preserve">
    <value>Albanese</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Tutti</value>
  </data>
  <data name="All Activity" xml:space="preserve">
    <value>Tutte le attività</value>
  </data>
  <data name="All Paertners" xml:space="preserve">
    <value>Elenco dei partner</value>
  </data>
  <data name="AlreadyPurchased" xml:space="preserve">
    <value>Hai già acquistato questo incarico. Un nuovo acquisto non è possibile.</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Quantità</value>
  </data>
  <data name="AmountMustBeGreaterThanZero" xml:space="preserve">
    <value>L’importo deve essere superiore a zero.</value>
  </data>
  <data name="Apartment" xml:space="preserve">
    <value>Appartamento</value>
  </data>
  <data name="Approx." xml:space="preserve">
    <value>Ca.</value>
  </data>
  <data name="Arabic" xml:space="preserve">
    <value>Arabo</value>
  </data>
  <data name="Archive" xml:space="preserve">
    <value>Archivio</value>
  </data>
  <data name="Archive Content" xml:space="preserve">
    <value>Contenuti archiviati</value>
  </data>
  <data name="Archive entry" xml:space="preserve">
    <value>Archivia voce:</value>
  </data>
  <data name="Are you sure you want to delete the selected entry?" xml:space="preserve">
    <value>Desideri davvero eliminare il record selezionato?</value>
  </data>
  <data name="Area" xml:space="preserve">
    <value>Area</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>Vuoi procedere con il ripristino?</value>
  </data>
  <data name="AssembleFurniture" xml:space="preserve">
    <value>Montare i mobili</value>
  </data>
  <data name="AT_Austria" xml:space="preserve">
    <value>AT–Austria</value>
  </data>
  <data name="Auszug" xml:space="preserve">
    <value>Uscita</value>
  </data>
  <data name="Background" xml:space="preserve">
    <value>Sfondo</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>Torna all’elenco</value>
  </data>
  <data name="BadRequestMessage" xml:space="preserve">
    <value>Qualcosa è andato storto. Riprova più tardi.</value>
  </data>
  <data name="BadRequestTitle" xml:space="preserve">
    <value>Richiesta non valida ⚠️</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>Saldo</value>
  </data>
  <data name="BalanceRechargeHistory" xml:space="preserve">
    <value>Cronologia delle ricariche del credito</value>
  </data>
  <data name="Balcony" xml:space="preserve">
    <value>Balcone</value>
  </data>
  <data name="Bank" xml:space="preserve">
    <value>Banca</value>
  </data>
  <data name="Bank Name" xml:space="preserve">
    <value>Nome della banca</value>
  </data>
  <data name="Basement, Cellar" xml:space="preserve">
    <value>Cantina</value>
  </data>
  <data name="Basic Cleaning" xml:space="preserve">
    <value>Pulizia di cantiere</value>
  </data>
  <data name="Blocked" xml:space="preserve">
    <value>Bloccato</value>
  </data>
  <data name="Both" xml:space="preserve">
    <value>Entrambi insieme</value>
  </data>
  <data name="BoxCity" xml:space="preserve">
    <value>CAP / Località</value>
  </data>
  <data name="Boxes" xml:space="preserve">
    <value>Scatole</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>Settore</value>
  </data>
  <data name="Building Cleaning" xml:space="preserve">
    <value>Pulizia degli edifici</value>
  </data>
  <data name="Business" xml:space="preserve">
    <value>Negozio</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Annulla</value>
  </data>
  <data name="Card number" xml:space="preserve">
    <value>Numero della carta</value>
  </data>
  <data name="Cardboard boxes" xml:space="preserve">
    <value>Scatole (ripetizione)</value>
  </data>
  <data name="CarpetCleaning" xml:space="preserve">
    <value>Pulizia dei tappeti</value>
  </data>
  <data name="Carpets" xml:space="preserve">
    <value>Tappeti</value>
  </data>
  <data name="Cartons" xml:space="preserve">
    <value>Scatole da trasloco</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Categoria</value>
  </data>
  <data name="Category (room)" xml:space="preserve">
    <value>Categoria (Stanza)</value>
  </data>
  <data name="Cellar" xml:space="preserve">
    <value>Cantina (ripetizione)</value>
  </data>
  <data name="Change password" xml:space="preserve">
    <value>Modifica password</value>
  </data>
  <data name="ChangePasswordHint" xml:space="preserve">
    <value>Lasciare questo campo vuoto se non si desidera alcuna modifica.</value>
  </data>
  <data name="Checkout" xml:space="preserve">
    <value>Vai alla cassa…</value>
  </data>
  <data name="CheckoutRedirectMessage" xml:space="preserve">
    <value>Stai per ricaricare il tuo credito. Vuoi continuare?</value>
  </data>
  <data name="Choose email content" xml:space="preserve">
    <value>Seleziona un messaggio</value>
  </data>
  <data name="ChooseActivity" xml:space="preserve">
    <value>Seleziona l’attività appropriata</value>
  </data>
  <data name="CH_Switzerland" xml:space="preserve">
    <value>CH–Svizzera</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Città</value>
  </data>
  <data name="Cleaning" xml:space="preserve">
    <value>Pulizia</value>
  </data>
  <data name="CleaningAndHandover" xml:space="preserve">
    <value>Pulizia e consegna di una</value>
  </data>
  <data name="CleaningDate" xml:space="preserve">
    <value>Data della pulizia</value>
  </data>
  <data name="CleaningType" xml:space="preserve">
    <value>Tipo di pulizia</value>
  </data>
  <data name="Click buttons to filter by activity" xml:space="preserve">
    <value>Fai clic sui pulsanti per filtrare per attività</value>
  </data>
  <data name="Click to buy" xml:space="preserve">
    <value>Acquistare</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Azienda</value>
  </data>
  <data name="Company Data" xml:space="preserve">
    <value>Dati dell’azienda</value>
  </data>
  <data name="Company Move" xml:space="preserve">
    <value>Trasloco aziendale</value>
  </data>
  <data name="Company Name" xml:space="preserve">
    <value>Nome dell’azienda</value>
  </data>
  <data name="CompanyDetails" xml:space="preserve">
    <value>Dettagli dell’azienda</value>
  </data>
  <data name="CompareAttribute_MustMatch" xml:space="preserve">
    <value>I campi {0} e {1} devono corrispondere.</value>
  </data>
  <data name="CompleteProfileMessage" xml:space="preserve">
    <value>Completa le informazioni del tuo profilo per completare correttamente la registrazione!</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Confermare</value>
  </data>
  <data name="Confirm New Password" xml:space="preserve">
    <value>Conferma la nuova password.</value>
  </data>
  <data name="Confirm Password" xml:space="preserve">
    <value>Conferma password</value>
  </data>
  <data name="ConfirmationCode" xml:space="preserve">
    <value>Codice di conferma</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Confermato</value>
  </data>
  <data name="ConfirmMessage" xml:space="preserve">
    <value>Ti abbiamo inviato un codice di conferma via e-mail. Inseriscilo qui.</value>
  </data>
  <data name="Construction Cleaning" xml:space="preserve">
    <value>Pulizia di cantiere</value>
  </data>
  <data name="Contact person" xml:space="preserve">
    <value>Persona di contatto</value>
  </data>
  <data name="ContactDetails" xml:space="preserve">
    <value>Dati di contatto</value>
  </data>
  <data name="ContactSubTitle" xml:space="preserve">
    <value>Informazioni dettagliate sulla richiesta di contatto</value>
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value>Contatto</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Creare</value>
  </data>
  <data name="Create a room" xml:space="preserve">
    <value>Registrare una stanza</value>
  </data>
  <data name="Create entry" xml:space="preserve">
    <value>Nuova voce</value>
  </data>
  <data name="Create room content" xml:space="preserve">
    <value>Aggiungere contenuti alla stanza</value>
  </data>
  <data name="Credit card" xml:space="preserve">
    <value>Carta di credito</value>
  </data>
  <data name="Croatian" xml:space="preserve">
    <value>Croato</value>
  </data>
  <data name="Crook" xml:space="preserve">
    <value>Truffatore</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Valuta</value>
  </data>
  <data name="Current Balance" xml:space="preserve">
    <value>Saldo attuale</value>
  </data>
  <data name="Current balance: CHF" xml:space="preserve">
    <value>Saldo attuale: CHF</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Cliente</value>
  </data>
  <data name="CustomerFocus" xml:space="preserve">
    <value>Focus cliente</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Scheda informativa</value>
  </data>
  <data name="Data imported successfully." xml:space="preserve">
    <value>I dati sono stati importati con successo.</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Data</value>
  </data>
  <data name="DateFrom" xml:space="preserve">
    <value>Eliminare movimenti: da</value>
  </data>
  <data name="Daten reinigen" xml:space="preserve">
    <value>Pulire i dati archiviati</value>
  </data>
  <data name="DateTo" xml:space="preserve">
    <value>a</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Elimina</value>
  </data>
  <data name="Delete record" xml:space="preserve">
    <value>Eliminare voce</value>
  </data>
  <data name="Delete Selected" xml:space="preserve">
    <value>Eliminare le voci selezionate</value>
  </data>
  <data name="Depending on the card type, you will find these in the marked position on the back of the card" xml:space="preserve">
    <value>A seconda del tipo di carta, troverai questo codice nel punto indicato sul retro della carta.</value>
  </data>
  <data name="DE_Germany" xml:space="preserve">
    <value>DE–Tedesco</value>
  </data>
  <data name="Different" xml:space="preserve">
    <value>Varie</value>
  </data>
  <data name="DismantleFurniture" xml:space="preserve">
    <value>Smontare i mobili</value>
  </data>
  <data name="DismantleLamp" xml:space="preserve">
    <value>Smontare la lampada</value>
  </data>
  <data name="Disposal" xml:space="preserve">
    <value>Smaltimento</value>
  </data>
  <data name="Distance" xml:space="preserve">
    <value>Distanza</value>
  </data>
  <data name="Doors" xml:space="preserve">
    <value>Porte</value>
  </data>
  <data name="DoReset" xml:space="preserve">
    <value>Sei sicuro?</value>
  </data>
  <data name="Download" xml:space="preserve">
    <value>Scaricare</value>
  </data>
  <data name="ExportExcel" xml:space="preserve">
    <value>Esporta Excel</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Modificare</value>
  </data>
  <data name="Edit archive data" xml:space="preserve">
    <value>Modificare i dati archiviati</value>
  </data>
  <data name="Edit category (rooms)" xml:space="preserve">
    <value>Modificare la categoria (stanze)</value>
  </data>
  <data name="Edit entry" xml:space="preserve">
    <value>Modificare l’indirizzo aziendale</value>
  </data>
  <data name="Edit messages" xml:space="preserve">
    <value>Modificare i messaggi</value>
  </data>
  <data name="Edit objects" xml:space="preserve">
    <value>Modificare gli oggetti</value>
  </data>
  <data name="Edit partner profile" xml:space="preserve">
    <value>Modificare il profilo partner</value>
  </data>
  <data name="Edit Profile Data" xml:space="preserve">
    <value>Modificare i dati del profilo</value>
  </data>
  <data name="Edit room content" xml:space="preserve">
    <value>Modificare i contenuti della stanza</value>
  </data>
  <data name="efh" xml:space="preserve">
    <value>Casa unifamiliare (EFH)</value>
  </data>
  <data name="EightFloor" xml:space="preserve">
    <value>8° piano</value>
  </data>
  <data name="Einzug" xml:space="preserve">
    <value>Entrata</value>
  </data>
  <data name="Electrician" xml:space="preserve">
    <value>Lavori elettrici</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="Email confirm" xml:space="preserve">
    <value>E-mail confermata</value>
  </data>
  <data name="Email senden" xml:space="preserve">
    <value>Email promozionali</value>
  </data>
  <data name="Email texts" xml:space="preserve">
    <value>Preparare i testi di corrispondenza</value>
  </data>
  <data name="EmailAddressAttribute_ValidationError" xml:space="preserve">
    <value>Il campo {0} non contiene un indirizzo e-mail valido.</value>
  </data>
  <data name="EmailAlreadyExistsError" xml:space="preserve">
    <value>L’indirizzo e-mail esiste già.</value>
  </data>
  <data name="EmailConfirmationMessage1" xml:space="preserve">
    <value>Ci sei quasi!</value>
  </data>
  <data name="EmailConfirmationMessage2" xml:space="preserve">
    <value>Solo un altro passaggio per iniziare!</value>
  </data>
  <data name="EmailConfirmationMessage3" xml:space="preserve">
    <value>Verifica e-mail</value>
  </data>
  <data name="EmailConfirmationMessage4" xml:space="preserve">
    <value>Un’e-mail con un codice di conferma ti verrà inviata.</value>
  </data>
  <data name="EmailConfirmationMessage5" xml:space="preserve">
    <value>Dopo l’invio, inserisci il codice di conferma per verificare la tua identità.</value>
  </data>
  <data name="EmailSenden" xml:space="preserve">
    <value>Invia email</value>
  </data>
  <data name="EmailSentSuccessfully" xml:space="preserve">
    <value>L’e-mail è stata inviata con successo.</value>
  </data>
  <data name="EmailToCustomer" xml:space="preserve">
    <value>E-mail al cliente</value>
  </data>
  <data name="EmptyOrdersMessage" xml:space="preserve">
    <value>Per favore riprova più tardi.</value>
  </data>
  <data name="EmptyOrdersTitle" xml:space="preserve">
    <value>Al momento non ci sono ordini disponibili.</value>
  </data>
  <data name="EmptyPurchasedOrders" xml:space="preserve">
    <value>Non hai ancora acquistato alcuna richiesta.</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Inglese</value>
  </data>
  <data name="Enter the ID number. a" xml:space="preserve">
    <value>Inserisci il numero ID</value>
  </data>
  <data name="EnterCompanyName" xml:space="preserve">
    <value>Inserisci il nome dell’azienda</value>
  </data>
  <data name="EnterYourEmail" xml:space="preserve">
    <value>Inserisci il tuo indirizzo e-mail</value>
  </data>
  <data name="Evaluation" xml:space="preserve">
    <value>Valutazione</value>
  </data>
  <data name="Event Cleaning" xml:space="preserve">
    <value>Pulizia per eventi</value>
  </data>
  <data name="Excellent" xml:space="preserve">
    <value>Eccellente</value>
  </data>
  <data name="Excerpt from" xml:space="preserve">
    <value>Uscita da</value>
  </data>
  <data name="Exec. Date" xml:space="preserve">
    <value>Data</value>
  </data>
  <data name="ExecDate" xml:space="preserve">
    <value>Data di esecuzione</value>
  </data>
  <data name="ExecutionDate" xml:space="preserve">
    <value>Data di esecuzione (ripetizione)</value>
  </data>
  <data name="Expiry Date" xml:space="preserve">
    <value>Data di scadenza</value>
  </data>
  <data name="External" xml:space="preserve">
    <value>Esterno</value>
  </data>
  <data name="Final Cleaning" xml:space="preserve">
    <value>Pulizia finale</value>
  </data>
  <data name="FiveFloor" xml:space="preserve">
    <value>5° piano</value>
  </data>
  <data name="Flexibility" xml:space="preserve">
    <value>Flessibilità</value>
  </data>
  <data name="Flexible" xml:space="preserve">
    <value>Flessibilità </value>
  </data>
  <data name="Floor" xml:space="preserve">
    <value>Piano</value>
  </data>
  <data name="Floor Cleaning" xml:space="preserve">
    <value>Pulizia dei pavimenti</value>
  </data>
  <data name="FloorAndPanels" xml:space="preserve">
    <value>Pavimenti e pannelli</value>
  </data>
  <data name="Focus" xml:space="preserve">
    <value>Focus cliente</value>
  </data>
  <data name="ForbiddenMessage" xml:space="preserve">
    <value>L’accesso a questa risorsa non è consentito.</value>
  </data>
  <data name="ForbiddenTitle" xml:space="preserve">
    <value>Accesso negato 🚫</value>
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value>Password dimenticata?</value>
  </data>
  <data name="ForgotPasswordMessage" xml:space="preserve">
    <value>Inserisci il tuo indirizzo e-mail. Ti invieremo le istruzioni per reimpostare la password.</value>
  </data>
  <data name="FourFloor" xml:space="preserve">
    <value>4° piano</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>Francese</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>Da</value>
  </data>
  <data name="Furniture" xml:space="preserve">
    <value>Mobili</value>
  </data>
  <data name="FurnitureAssembly" xml:space="preserve">
    <value>Montaggio mobili</value>
  </data>
  <data name="FurnitureLift" xml:space="preserve">
    <value>Montacarichi per mobili</value>
  </data>
  <data name="Garage" xml:space="preserve">
    <value>Garage</value>
  </data>
  <data name="Garden" xml:space="preserve">
    <value>Giardino</value>
  </data>
  <data name="Gastronomy" xml:space="preserve">
    <value>Gastronomia</value>
  </data>
  <data name="General statistics" xml:space="preserve">
    <value>Statistiche generali</value>
  </data>
  <data name="German" xml:space="preserve">
    <value>Tedesco</value>
  </data>
  <data name="Gisper" xml:space="preserve">
    <value>Stuccatore</value>
  </data>
  <data name="Good" xml:space="preserve">
    <value>Buono</value>
  </data>
  <data name="GreaterTenFloor" xml:space="preserve">
    <value>10° piano</value>
  </data>
  <data name="HandOverDate" xml:space="preserve">
    <value>Data di consegna</value>
  </data>
  <data name="HasInventory" xml:space="preserve">
    <value>Inventario</value>
  </data>
  <data name="HaveAccount" xml:space="preserve">
    <value>Non hai ancora un account?</value>
  </data>
  <data name="Healthcare" xml:space="preserve">
    <value>Sanità / Settore sanitario</value>
  </data>
  <data name="HeatingAndEnergy" xml:space="preserve">
    <value>Riscaldamento ed energia</value>
  </data>
  <data name="HeavyLoad" xml:space="preserve">
    <value>Carico pesante</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Buongiorno</value>
  </data>
  <data name="HighPressure" xml:space="preserve">
    <value>Alta pressione</value>
  </data>
  <data name="Hotel Cleaning" xml:space="preserve">
    <value>Pulizia alberghiera</value>
  </data>
  <data name="Hotel industry" xml:space="preserve">
    <value>Hotellerie</value>
  </data>
  <data name="House Keeping" xml:space="preserve">
    <value>Custodia</value>
  </data>
  <data name="House maintenance" xml:space="preserve">
    <value>Custodia</value>
  </data>
  <data name="I have read and agree to the general terms and conditions for purchasing inquiries online via TaskDotNets" xml:space="preserve">
    <value>Confermo di aver letto e accettato i termini e le condizioni generali (CG) per l’acquisto online delle richieste su TaskDotNets.com.</value>
  </data>
  <data name="IBAN" xml:space="preserve">
    <value>Numero IBAN</value>
  </data>
  <data name="ID Number" xml:space="preserve">
    <value>Numero ID</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Immagine</value>
  </data>
  <data name="IndividualActivity" xml:space="preserve">
    <value>Attività personalizzata</value>
  </data>
  <data name="Industrial Cleaning" xml:space="preserve">
    <value>Pulizia industriale</value>
  </data>
  <data name="Information Sheet" xml:space="preserve">
    <value>Scheda informativa</value>
  </data>
  <data name="Inspection" xml:space="preserve">
    <value>Sopralluogo</value>
  </data>
  <data name="InstallLamp" xml:space="preserve">
    <value>Installazione lampade</value>
  </data>
  <data name="Internal" xml:space="preserve">
    <value>Interno</value>
  </data>
  <data name="Internal/External" xml:space="preserve">
    <value>Interno / Esterno</value>
  </data>
  <data name="InternalServerErrorMessage" xml:space="preserve">
    <value>Errore interno del server. Si prega di contattare il supporto se il problema persiste.</value>
  </data>
  <data name="InternalServerErrorTitle" xml:space="preserve">
    <value>Errore interno del server 💥</value>
  </data>
  <data name="International Move" xml:space="preserve">
    <value>Trasloco internazionale</value>
  </data>
  <data name="InvalidAmount" xml:space="preserve">
    <value>Importo non valido</value>
  </data>
  <data name="InvalidInput" xml:space="preserve">
    <value>Inserimento non valido</value>
  </data>
  <data name="Inventory" xml:space="preserve">
    <value>Inventario (stanze)</value>
  </data>
  <data name="Inventory Items" xml:space="preserve">
    <value>Inventario (contenuto)</value>
  </data>
  <data name="Inventory list" xml:space="preserve">
    <value>Lista dell'inventario</value>
  </data>
  <data name="InventoryItem" xml:space="preserve">
    <value>Oggetto dell'inventario</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>Italiano</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Oggetti</value>
  </data>
  <data name="ItemsList" xml:space="preserve">
    <value>Aggiungere e modificare i contenuti delle stanze</value>
  </data>
  <data name="KantonWorkingIn" xml:space="preserve">
    <value>Seleziona i cantoni in cui desideri lavorare</value>
  </data>
  <data name="KitchenConstruction" xml:space="preserve">
    <value>Costruzione di cucine</value>
  </data>
  <data name="Kurdish" xml:space="preserve">
    <value>Curdo</value>
  </data>
  <data name="Laminate" xml:space="preserve">
    <value>Laminato</value>
  </data>
  <data name="Lamps" xml:space="preserve">
    <value>Lampade</value>
  </data>
  <data name="Land" xml:space="preserve">
    <value>Paese</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Lingua</value>
  </data>
  <data name="Lift" xml:space="preserve">
    <value>Ascensore</value>
  </data>
  <data name="Linoleum" xml:space="preserve">
    <value>Linoleum</value>
  </data>
  <data name="Locksmith" xml:space="preserve">
    <value>Locksmith services</value>
  </data>
  <data name="Log Out" xml:space="preserve">
    <value>Disconnettersi</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Accedere</value>
  </data>
  <data name="loginDesc" xml:space="preserve">
    <value>Tutto sotto controllo – più intelligente, più veloce, meglio organizzato</value>
  </data>
  <data name="LoginHeader" xml:space="preserve">
    <value>Qui ottieni accesso completo a tutte le richieste e informazioni.</value>
  </data>
  <data name="Maintenance Cleaning" xml:space="preserve">
    <value>Pulizia di manutenzione</value>
  </data>
  <data name="Manage Users" xml:space="preserve">
    <value>Gestire utenti</value>
  </data>
  <data name="Maximum offers" xml:space="preserve">
    <value>Numero di vendite consentite</value>
  </data>
  <data name="Mechanic" xml:space="preserve">
    <value>Lavori meccanici</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Messaggio</value>
  </data>
  <data name="Miss" xml:space="preserve">
    <value>Signorina</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Cellulare</value>
  </data>
  <data name="MoreWork" xml:space="preserve">
    <value>Ulteriori lavori</value>
  </data>
  <data name="Move And Clean" xml:space="preserve">
    <value>Trasloco e pulizia</value>
  </data>
  <data name="MoveFrom" xml:space="preserve">
    <value>Uscita da</value>
  </data>
  <data name="MoveTo" xml:space="preserve">
    <value>a</value>
  </data>
  <data name="Moving" xml:space="preserve">
    <value>Trasloco</value>
  </data>
  <data name="Moving &amp; Cleaning" xml:space="preserve">
    <value>Trasloco &amp; pulizia</value>
  </data>
  <data name="Moving and Cleaning" xml:space="preserve">
    <value>Trasloco e pulizia</value>
  </data>
  <data name="Moving in" xml:space="preserve">
    <value>Trasloco a</value>
  </data>
  <data name="MovingAndCleaning" xml:space="preserve">
    <value>Trasloco &amp; pulizia</value>
  </data>
  <data name="MovingBoxes" xml:space="preserve">
    <value>Scatole per il trasloco</value>
  </data>
  <data name="Mr" xml:space="preserve">
    <value>Signore</value>
  </data>
  <data name="Mrs" xml:space="preserve">
    <value>Signora</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nome</value>
  </data>
  <data name="Name in English" xml:space="preserve">
    <value>Oggetto in inglese</value>
  </data>
  <data name="Name in French" xml:space="preserve">
    <value>Oggetto in francese</value>
  </data>
  <data name="Name in German" xml:space="preserve">
    <value>Oggetto in tedesco</value>
  </data>
  <data name="Name in Italian" xml:space="preserve">
    <value>Oggetto in italiano</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Nuovo</value>
  </data>
  <data name="New entry" xml:space="preserve">
    <value>Nuova voce</value>
  </data>
  <data name="New message" xml:space="preserve">
    <value>Nuovo messaggio</value>
  </data>
  <data name="New Password" xml:space="preserve">
    <value>Nuova password</value>
  </data>
  <data name="NewPasswordMessage" xml:space="preserve">
    <value>Inserisci e conferma la nuova password.</value>
  </data>
  <data name="NewRequest" xml:space="preserve">
    <value>Nuova richiesta</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Avanti</value>
  </data>
  <data name="NineFloor" xml:space="preserve">
    <value>9° piano</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="NotEnoughBalance" xml:space="preserve">
    <value>Credito insufficiente, si prega di ricaricare il proprio saldo.</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Note</value>
  </data>
  <data name="NotFoundMessage" xml:space="preserve">
    <value>Non siamo riusciti a trovare la pagina richiesta.</value>
  </data>
  <data name="NotFoundTitle" xml:space="preserve">
    <value>Pagina non trovata ⚠️</value>
  </data>
  <data name="Nothing" xml:space="preserve">
    <value>Nessuno</value>
  </data>
  <data name="NotRequired" xml:space="preserve">
    <value>Non richiesto</value>
  </data>
  <data name="Number" xml:space="preserve">
    <value>Quantità</value>
  </data>
  <data name="Object" xml:space="preserve">
    <value>Oggetto</value>
  </data>
  <data name="Office Cleaning" xml:space="preserve">
    <value>Pulizia uffici</value>
  </data>
  <data name="Office Rooms" xml:space="preserve">
    <value>Uffici</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Old Paassword" xml:space="preserve">
    <value>Vecchia password</value>
  </data>
  <data name="OneDay" xml:space="preserve">
    <value>1 giorno</value>
  </data>
  <data name="OneFloor" xml:space="preserve">
    <value>1° piano</value>
  </data>
  <data name="OneMonth" xml:space="preserve">
    <value>1 mese</value>
  </data>
  <data name="OneWeek" xml:space="preserve">
    <value>1 settimana</value>
  </data>
  <data name="Order-Nr" xml:space="preserve">
    <value>Numero richiesta</value>
  </data>
  <data name="OrderCountTitle" xml:space="preserve">
    <value>Modulo di controllo della richiesta</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>Numero d'ordine</value>
  </data>
  <data name="OrdersList" xml:space="preserve">
    <value>Elenco ordini</value>
  </data>
  <data name="OrderType" xml:space="preserve">
    <value>Tipo di incarico</value>
  </data>
  <data name="OurPartner" xml:space="preserve">
    <value>Il nostro partner</value>
  </data>
  <data name="Owner" xml:space="preserve">
    <value>Proprietario</value>
  </data>
  <data name="Packing" xml:space="preserve">
    <value>Imballare</value>
  </data>
  <data name="Pages" xml:space="preserve">
    <value>Pagine</value>
  </data>
  <data name="Paid costs CHF" xml:space="preserve">
    <value>Costi pagati CHF</value>
  </data>
  <data name="Paid costs CHF1" xml:space="preserve">
    <value>Costi pagati CHF</value>
  </data>
  <data name="Painting" xml:space="preserve">
    <value>Tinteggiatura</value>
  </data>
  <data name="Painting &amp; Gipser" xml:space="preserve">
    <value>Pittori &amp; stuccatori</value>
  </data>
  <data name="PaintingAndGisper" xml:space="preserve">
    <value>Pittori &amp; stuccatori</value>
  </data>
  <data name="Parquet" xml:space="preserve">
    <value>Parquet</value>
  </data>
  <data name="Partner Data Updated Successfully" xml:space="preserve">
    <value>Dati partner aggiornati con successo</value>
  </data>
  <data name="Partner Profile" xml:space="preserve">
    <value>Profilo partner</value>
  </data>
  <data name="Partner Status" xml:space="preserve">
    <value>Stato del partner</value>
  </data>
  <data name="Partner-No" xml:space="preserve">
    <value>N. partner</value>
  </data>
  <data name="PartnerBalanceHistory" xml:space="preserve">
    <value>Cicli di carico</value>
  </data>
  <data name="PartnerDataUpdatedSuccessfully" xml:space="preserve">
    <value>Dati partner aggiornati con successo.</value>
  </data>
  <data name="PartnerList" xml:space="preserve">
    <value>Elenco partner</value>
  </data>
  <data name="PartnerOrders" xml:space="preserve">
    <value>Incarichi partner</value>
  </data>
  <data name="PartnerOrdersReport" xml:space="preserve">
    <value>Rapporto incarichi partner</value>
  </data>
  <data name="Partner_Block_UID" xml:space="preserve">
    <value>Spiacenti, a causa di precedenti violazioni sei stato escluso dall'utilizzo della nostra piattaforma.
La registrazione non è quindi possibile.
Per ulteriori informazioni, <NAME_EMAIL>.</value>
  </data>
  <data name="Partner_Dashboard01" xml:space="preserve">
    <value>Selezioni le richieste adatte in base al tipo di attività e alla regione. Le verranno mostrate solo quelle rilevanti. </value>
  </data>
  <data name="Partner_Dashboard02" xml:space="preserve">
    <value>Prenoti rapidamente e contatti direttamente il cliente per fissare un appuntamento o inviare un'offerta.</value>
  </data>
  <data name="Partner_Dashboard03" xml:space="preserve">
    <value>Il cliente si aspetta un'offerta adeguata, affidabilità e puntualità.</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="Pay CHF" xml:space="preserve">
    <value>Pagare CHF</value>
  </data>
  <data name="Pay now" xml:space="preserve">
    <value>Paga ora</value>
  </data>
  <data name="Payment" xml:space="preserve">
    <value>Pagamento</value>
  </data>
  <data name="PaymentFehler" xml:space="preserve">
    <value>Si è verificato un errore durante il pagamento.</value>
  </data>
  <data name="PaymentMethod" xml:space="preserve">
    <value>Metodo di pagamento</value>
  </data>
  <data name="PaymentSuccesfully" xml:space="preserve">
    <value>Il pagamento è stato completato con successo.</value>
  </data>
  <data name="Period" xml:space="preserve">
    <value>Periodo</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Telefono</value>
  </data>
  <data name="Piano" xml:space="preserve">
    <value>Pianoforte</value>
  </data>
  <data name="Plates" xml:space="preserve">
    <value>Piastrelle</value>
  </data>
  <data name="Please choose a payment method!" xml:space="preserve">
    <value>Seleziona un metodo di pagamento!</value>
  </data>
  <data name="Please pay for the selected order" xml:space="preserve">
    <value>Paga ora l'incarico selezionato.</value>
  </data>
  <data name="PleaseAcceptTerms" xml:space="preserve">
    <value>Accetta i termini e le condizioni generali.</value>
  </data>
  <data name="Plumbing" xml:space="preserve">
    <value>Travaux de plomberie</value>
  </data>
  <data name="PName" xml:space="preserve">
    <value>Persona di contatto</value>
  </data>
  <data name="Portuguese" xml:space="preserve">
    <value>Portoghese</value>
  </data>
  <data name="PostBox" xml:space="preserve">
    <value>CAP</value>
  </data>
  <data name="Preisfuer" xml:space="preserve">
    <value>Prezzo per</value>
  </data>
  <data name="Press key to show the list of desired activity" xml:space="preserve">
    <value>Premi il pulsante per visualizzare l'elenco dell'attività desiderata.</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Prezzo</value>
  </data>
  <data name="Price of the cleaning request" xml:space="preserve">
    <value>Prezzo della richiesta di pulizia</value>
  </data>
  <data name="Price of the combined request" xml:space="preserve">
    <value>Prezzo della richiesta combinata</value>
  </data>
  <data name="Price of the moving request" xml:space="preserve">
    <value>Prezzo della richiesta di trasloco</value>
  </data>
  <data name="Price of the painting request" xml:space="preserve">
    <value>Prezzo della richiesta di pittura</value>
  </data>
  <data name="Price of the plastering request" xml:space="preserve">
    <value>Prezzo della richiesta di intonacatura</value>
  </data>
  <data name="Price/Quality" xml:space="preserve">
    <value>Prezzo / Qualità</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Stampa</value>
  </data>
  <data name="PrintReport" xml:space="preserve">
    <value>Stampa rapporto</value>
  </data>
  <data name="Privacy Policy" xml:space="preserve">
    <value>nformativa sulla privacy</value>
  </data>
  <data name="Private Move" xml:space="preserve">
    <value>Trasloco privato</value>
  </data>
  <data name="Process an order" xml:space="preserve">
    <value>Piattaforma TaskDotNet: &lt;br /&gt;                                                                                       La soluzione per lavorare in modo produttivo</value>
  </data>
  <data name="Purchased Activites" xml:space="preserve">
    <value>Attività acquistate</value>
  </data>
  <data name="Purchased orders" xml:space="preserve">
    <value>Ordini acquistati</value>
  </data>
  <data name="PurchaseDate" xml:space="preserve">
    <value>Data di acquisto</value>
  </data>
  <data name="Quality" xml:space="preserve">
    <value>Qualità</value>
  </data>
  <data name="RangeAttribute_ValidationError" xml:space="preserve">
    <value>Il campo {0} deve essere compreso tra {1} e {2}.</value>
  </data>
  <data name="ReadTermsAndCondotions" xml:space="preserve">
    <value>Ho letto e accetto i Termini e Condizioni generali (TCG).</value>
  </data>
  <data name="Recharge credit" xml:space="preserve">
    <value>Ricaricare il credito</value>
  </data>
  <data name="RechargeCreditHead1" xml:space="preserve">
    <value>Qui hai la possibilità di ricaricare il tuo credito, così non dovrai pagare ogni volta che effettui un acquisto.</value>
  </data>
  <data name="RechargeCreditHead2" xml:space="preserve">
    <value>I tuoi dati personali verranno utilizzati per elaborare il pagamento, migliorare la tua esperienza utente su questo sito e fornirti informazioni trasparenti sull'utilizzo dei tuoi dati.</value>
  </data>
  <data name="RechargeCreditHead3" xml:space="preserve">
    <value>Ti invitiamo a leggere la nostra</value>
  </data>
  <data name="RechargeFailedMessage" xml:space="preserve">
    <value>Ricarica fallita. Riprova più tardi.</value>
  </data>
  <data name="RechargeMessage" xml:space="preserve">
    <value>Il credito è stato aggiunto con successo. Grazie mille!</value>
  </data>
  <data name="RechargeSuccessfullyMessage" xml:space="preserve">
    <value>Il credito è stato ricaricato con successo.</value>
  </data>
  <data name="Recurring Cleaning" xml:space="preserve">
    <value>Pulizia ricorrente</value>
  </data>
  <data name="RefrigerationTechnician" xml:space="preserve">
    <value>Servizi di refrigerazione</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Registrati</value>
  </data>
  <data name="Register here" xml:space="preserve">
    <value>Registrati </value>
  </data>
  <data name="Register now" xml:space="preserve">
    <value>Registrati ora</value>
  </data>
  <data name="RegisterHeader1" xml:space="preserve">
    <value>La piattaforma per</value>
  </data>
  <data name="RegisterHeader2" xml:space="preserve">
    <value> la massima produttività.</value>
  </data>
  <data name="RegisterHeader3" xml:space="preserve">
    <value>Connessa in modo efficiente. </value>
  </data>
  <data name="RegisterHeader4" xml:space="preserve">
    <value>Realizzata con efficacia.</value>
  </data>
  <data name="RegisterHeader5" xml:space="preserve">
    <value>Inserisci le tue credenziali per registrarti su TaskDotNet.</value>
  </data>
  <data name="Regular Cleaning" xml:space="preserve">
    <value>Pulizia regolare</value>
  </data>
  <data name="Remember Me" xml:space="preserve">
    <value>Rimani connesso</value>
  </data>
  <data name="Request List" xml:space="preserve">
    <value>Elenco delle richieste</value>
  </data>
  <data name="RequiredAttribute_ValidationError" xml:space="preserve">
    <value>Il campo {0} è obbligatorio.</value>
  </data>
  <data name="ResetDashboard" xml:space="preserve">
    <value>Reimposta dashboard</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Reimposta password</value>
  </data>
  <data name="ResetPasswordMessage" xml:space="preserve">
    <value>Il link per reimpostare la password è stato inviato. Controlla la tua casella di Posta in arrivo.</value>
  </data>
  <data name="ResetTitle" xml:space="preserve">
    <value>Pulire i dati del modulo</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Diritto di accesso</value>
  </data>
  <data name="Roofer" xml:space="preserve">
    <value>Lavori di copertura</value>
  </data>
  <data name="Room" xml:space="preserve">
    <value>Stanza</value>
  </data>
  <data name="RoomList" xml:space="preserve">
    <value>Elenco delle stanze principali</value>
  </data>
  <data name="Rooms" xml:space="preserve">
    <value>Stanza</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>Russo</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Salva</value>
  </data>
  <data name="Save changes" xml:space="preserve">
    <value>Salva modifiche</value>
  </data>
  <data name="Select an activity" xml:space="preserve">
    <value>Seleziona un’attività</value>
  </data>
  <data name="Select Branch" xml:space="preserve">
    <value>Seleziona il settore</value>
  </data>
  <data name="Select Template" xml:space="preserve">
    <value>Seleziona un modello</value>
  </data>
  <data name="Select the Activity you want" xml:space="preserve">
    <value>Seleziona le attività desiderate</value>
  </data>
  <data name="Select the cantons in which you would like to work here" xml:space="preserve">
    <value>Seleziona qui i cantoni in cui desideri lavorare.</value>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Invia</value>
  </data>
  <data name="SendEmail" xml:space="preserve">
    <value>Invia e-mail</value>
  </data>
  <data name="SendEmailTo" xml:space="preserve">
    <value>Invia un’e-mail al partner</value>
  </data>
  <data name="SendMessageToTheCustomer" xml:space="preserve">
    <value>Invia un messaggio al cliente</value>
  </data>
  <data name="SendResetLink" xml:space="preserve">
    <value>Invia link di reimpostazione</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Impostazioni</value>
  </data>
  <data name="SevenFloor" xml:space="preserve">
    <value>7° piano</value>
  </data>
  <data name="Show all Activity" xml:space="preserve">
    <value>Mostra tutte le attività</value>
  </data>
  <data name="Show all Archive data" xml:space="preserve">
    <value>Mostra tutti i dati archiviati</value>
  </data>
  <data name="Show all Inventory" xml:space="preserve">
    <value>Modifica stanze</value>
  </data>
  <data name="Show all Statistics" xml:space="preserve">
    <value>Mostra tutte le statistiche</value>
  </data>
  <data name="Sightseeing" xml:space="preserve">
    <value>Sopralluogo</value>
  </data>
  <data name="SixFloor" xml:space="preserve">
    <value>6° piano</value>
  </data>
  <data name="SmallTransport" xml:space="preserve">
    <value>Trasporto leggero</value>
  </data>
  <data name="SoilType" xml:space="preserve">
    <value>Tipo di pavimento</value>
  </data>
  <data name="Sold" xml:space="preserve">
    <value>Venduto</value>
  </data>
  <data name="SomeThingWentWrong" xml:space="preserve">
    <value>Qualcosa è andato storto. Riprova più tardi.</value>
  </data>
  <data name="Space" xml:space="preserve">
    <value>Stanza</value>
  </data>
  <data name="Spanish" xml:space="preserve">
    <value>Spagnolo</value>
  </data>
  <data name="Start Date" xml:space="preserve">
    <value>Data di inizio</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Data di inizio</value>
  </data>
  <data name="Statistics" xml:space="preserve">
    <value>Statistiche</value>
  </data>
  <data name="StatisticsPageTitle" xml:space="preserve">
    <value>Questa tabella mostra i movimenti di vendita per tutti i prodotti e le attività annunciate durante l’anno in corso.</value>
  </data>
  <data name="StatisticsPageTitlePartner" xml:space="preserve">
    <value>Questa tabella mostra i movimenti di acquisto per tutti i prodotti (attività) acquistati nell’anno in corso.</value>
  </data>
  <data name="StatisticsPageTitlePartner2" xml:space="preserve">
    <value>Gli acquisti del partner per l’anno in corso:</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Stato</value>
  </data>
  <data name="Storage" xml:space="preserve">
    <value>Deposito</value>
  </data>
  <data name="Street" xml:space="preserve">
    <value>Via</value>
  </data>
  <data name="StringLengthAttribute_ValidationError" xml:space="preserve">
    <value>Il campo {0} deve essere una stringa con una lunghezza massima di {1} caratteri.</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Oggetto</value>
  </data>
  <data name="System setup" xml:space="preserve">
    <value>Impostazioni di sistema</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Il tuo team di TaskDotNet</value>
  </data>
  <data name="TenFloor" xml:space="preserve">
    <value>10° piano</value>
  </data>
  <data name="TermsNotAccepted" xml:space="preserve">
    <value>Condizioni generali non accettate</value>
  </data>
  <data name="Thank you for your payment..." xml:space="preserve">
    <value>Grazie mille per il tuo pagamento.</value>
  </data>
  <data name="The achievements of partnere for the current month" xml:space="preserve">
    <value>Servizi forniti dai partner durante l’anno in corso.</value>
  </data>
  <data name="The total: CHF" xml:space="preserve">
    <value>Importo totale: CHF</value>
  </data>
  <data name="PartnerDashboardMessage" xml:space="preserve">
    <value>Questo progetto è destinato alla collaborazione con "TaskDotNet.com" e aiuta i partner a monitorare gli ordini, analizzare le statistiche e prendere decisioni informate sulla base dei dati aggiornati.</value>
  </data>
  <data name="This request will be deleted after archiving!" xml:space="preserve">
    <value>Questa richiesta verrà eliminata dopo l’archiviazione!</value>
  </data>
  <data name="This table displays the work activity statistics for the current year" xml:space="preserve">
    <value>Questa tabella mostra le statistiche delle attività lavorative per l’anno in corso.</value>
  </data>
  <data name="ThisIsRequiredRequest" xml:space="preserve">
    <value>Ceci est la demande concrète.</value>
  </data>
  <data name="ThreeDays" xml:space="preserve">
    <value>3 jours</value>
  </data>
  <data name="ThreeFloor" xml:space="preserve">
    <value>3ᵉ étage</value>
  </data>
  <data name="ThreeWeeks" xml:space="preserve">
    <value>3 semaines</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Titre</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>À</value>
  </data>
  <data name="To which branch" xml:space="preserve">
    <value>Seleziona un settore</value>
  </data>
  <data name="Top Up Balance" xml:space="preserve">
    <value>Recharger le crédit</value>
  </data>
  <data name="Total volume" xml:space="preserve">
    <value>Volume total</value>
  </data>
  <data name="toTheDrainEdge" xml:space="preserve">
    <value>vers la zone d’évacuation</value>
  </data>
  <data name="toTheLoadingEdge" xml:space="preserve">
    <value>vers la zone de chargement</value>
  </data>
  <data name="Turkish" xml:space="preserve">
    <value>Turc</value>
  </data>
  <data name="TwoDays" xml:space="preserve">
    <value>2 jours</value>
  </data>
  <data name="TwoFloor" xml:space="preserve">
    <value>2ᵉ étage</value>
  </data>
  <data name="TwoWeeks" xml:space="preserve">
    <value>2 semaines</value>
  </data>
  <data name="UID" xml:space="preserve">
    <value>Numéro IDE</value>
  </data>
  <data name="UnauthorizedMessage" xml:space="preserve">
    <value>Vous n’êtes malheureusement pas autorisé à accéder à cette page. Veuillez retourner à la page d’accueil.</value>
  </data>
  <data name="UnauthorizedTitle" xml:space="preserve">
    <value>Vous n’êtes pas autorisé ! 🔐</value>
  </data>
  <data name="Unit" xml:space="preserve">
    <value>Unité</value>
  </data>
  <data name="Unpacking" xml:space="preserve">
    <value>Déballer</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Mettre à jour</value>
  </data>
  <data name="Upload File" xml:space="preserve">
    <value>Téléverser un fichier</value>
  </data>
  <data name="Useless" xml:space="preserve">
    <value>Inutile</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Utilisateur</value>
  </data>
  <data name="UsersList" xml:space="preserve">
    <value>Liste des utilisateurs</value>
  </data>
  <data name="VerifyAccount" xml:space="preserve">
    <value>Veuillez vérifier votre compte.</value>
  </data>
  <data name="View All" xml:space="preserve">
    <value>Afficher tout</value>
  </data>
  <data name="View order" xml:space="preserve">
    <value>Aperçu de la demande</value>
  </data>
  <data name="Viewing" xml:space="preserve">
    <value>Visite</value>
  </data>
  <data name="ViewingDate" xml:space="preserve">
    <value>Date de la visite</value>
  </data>
  <data name="Volume (m3)" xml:space="preserve">
    <value>Volume (m³)</value>
  </data>
  <data name="Volume in m3" xml:space="preserve">
    <value>Volume en m³</value>
  </data>
  <data name="Walls" xml:space="preserve">
    <value>Murs</value>
  </data>
  <data name="WallsAndCeilings" xml:space="preserve">
    <value>Murs et plafonds</value>
  </data>
  <data name="Washroom" xml:space="preserve">
    <value>Buanderie</value>
  </data>
  <data name="Weak" xml:space="preserve">
    <value>Faible</value>
  </data>
  <data name="Website" xml:space="preserve">
    <value>Site web</value>
  </data>
  <data name="WelcomeBack" xml:space="preserve">
    <value>Bienvenue, partenaire !</value>
  </data>
  <data name="Welder" xml:space="preserve">
    <value>Lavori di saldatura</value>
  </data>
  <data name="Window Cleaning" xml:space="preserve">
    <value>Nettoyage des vitres</value>
  </data>
  <data name="Windows" xml:space="preserve">
    <value>Fenêtres</value>
  </data>
  <data name="Windows Cleaning" xml:space="preserve">
    <value>Nettoyer les fenêtres</value>
  </data>
  <data name="WithInventoryList" xml:space="preserve">
    <value>avec liste d’inventaire</value>
  </data>
  <data name="Workers" xml:space="preserve">
    <value>Ouvrier</value>
  </data>
  <data name="Workspace" xml:space="preserve">
    <value>Zone</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="YesOn" xml:space="preserve">
    <value>Oui, le :</value>
  </data>
  <data name="YouWantToPurchaseThisOrder" xml:space="preserve">
    <value>Remarque : L’achat de cette commande déduira {0} de votre crédit.</value>
  </data>
  <data name="AdminDashboardHeader" xml:space="preserve">
    <value>Controllo completo sulla piattaforma TaskDotNet</value>
  </data>
  <data name="AdminDashboardHeader2" xml:space="preserve">
    <value>Questa è una pagina statistica che mostra le  
interazioni e le attività di tutti i partner sulla  
piattaforma.</value>
  </data>
  <data name="CreateUser" xml:space="preserve">
    <value>Crea utente</value>
  </data>
  <data name="Handyman" xml:space="preserve">
    <value>Tuttofare</value>
  </data>
  <data name="RequestControl" xml:space="preserve">
    <value>Controllo delle richieste</value>
  </data>
  <data name="UpdateUser" xml:space="preserve">
    <value>Aggiornare l’utente</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Indietro</value>
  </data>
  <data name="Credits" xml:space="preserve">
    <value>Accrediti</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filtro</value>
  </data>
  <data name="NoCreditsFound" xml:space="preserve">
    <value>Nessun credito trovato per il periodo selezionato.</value>
  </data>
  <data name="Partnername" xml:space="preserve">
    <value>Nome del partner</value>
  </data>
  <data name="PaymentWay" xml:space="preserve">
    <value>Metodo di pagamento</value>
  </data>
</root>