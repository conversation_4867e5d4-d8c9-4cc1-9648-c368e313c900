﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Account" xml:space="preserve">
    <value />
  </data>
  <data name="Account Data" xml:space="preserve">
    <value />
  </data>
  <data name="Account Settings" xml:space="preserve">
    <value />
  </data>
  <data name="Actions" xml:space="preserve">
    <value />
  </data>
  <data name="Active" xml:space="preserve">
    <value />
  </data>
  <data name="Activites setup" xml:space="preserve">
    <value />
  </data>
  <data name="Activities List" xml:space="preserve">
    <value />
  </data>
  <data name="Activity" xml:space="preserve">
    <value />
  </data>
  <data name="ActivityDetails" xml:space="preserve">
    <value />
  </data>
  <data name="ActivityDetailsTitle" xml:space="preserve">
    <value />
  </data>
  <data name="Add to archive" xml:space="preserve">
    <value />
  </data>
  <data name="AddAmount" xml:space="preserve">
    <value />
  </data>
  <data name="AdditionalServices" xml:space="preserve">
    <value />
  </data>
  <data name="Additive" xml:space="preserve">
    <value />
  </data>
  <data name="Address" xml:space="preserve">
    <value />
  </data>
  <data name="Administrator" xml:space="preserve">
    <value />
  </data>
  <data name="AdvertisingList" xml:space="preserve">
    <value />
  </data>
  <data name="Albanian" xml:space="preserve">
    <value />
  </data>
  <data name="All" xml:space="preserve">
    <value />
  </data>
  <data name="All Activity" xml:space="preserve">
    <value />
  </data>
  <data name="All Paertners" xml:space="preserve">
    <value />
  </data>
  <data name="AlreadyPurchased" xml:space="preserve">
    <value />
  </data>
  <data name="Amount" xml:space="preserve">
    <value />
  </data>
  <data name="AmountMustBeGreaterThanZero" xml:space="preserve">
    <value />
  </data>
  <data name="Apartment" xml:space="preserve">
    <value />
  </data>
  <data name="Approx." xml:space="preserve">
    <value />
  </data>
  <data name="Arabic" xml:space="preserve">
    <value />
  </data>
  <data name="Archive" xml:space="preserve">
    <value />
  </data>
  <data name="Archive Content" xml:space="preserve">
    <value />
  </data>
  <data name="Archive entry" xml:space="preserve">
    <value />
  </data>
  <data name="Are you sure you want to delete the selected entry?" xml:space="preserve">
    <value />
  </data>
  <data name="Area" xml:space="preserve">
    <value />
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value />
  </data>
  <data name="AssembleFurniture" xml:space="preserve">
    <value />
  </data>
  <data name="AT_Austria" xml:space="preserve">
    <value />
  </data>
  <data name="Auszug" xml:space="preserve">
    <value />
  </data>
  <data name="Background" xml:space="preserve">
    <value />
  </data>
  <data name="BackToList" xml:space="preserve">
    <value />
  </data>
  <data name="BadRequestMessage" xml:space="preserve">
    <value />
  </data>
  <data name="BadRequestTitle" xml:space="preserve">
    <value />
  </data>
  <data name="Balance" xml:space="preserve">
    <value />
  </data>
  <data name="BalanceRechargeHistory" xml:space="preserve">
    <value />
  </data>
  <data name="Balcony" xml:space="preserve">
    <value />
  </data>
  <data name="Bank" xml:space="preserve">
    <value />
  </data>
  <data name="Bank Name" xml:space="preserve">
    <value />
  </data>
  <data name="Basement, Cellar" xml:space="preserve">
    <value />
  </data>
  <data name="Basic Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Blocked" xml:space="preserve">
    <value />
  </data>
  <data name="Both" xml:space="preserve">
    <value />
  </data>
  <data name="BoxCity" xml:space="preserve">
    <value />
  </data>
  <data name="Boxes" xml:space="preserve">
    <value />
  </data>
  <data name="Branch" xml:space="preserve">
    <value />
  </data>
  <data name="Building Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Business" xml:space="preserve">
    <value />
  </data>
  <data name="Cancel" xml:space="preserve">
    <value />
  </data>
  <data name="Card number" xml:space="preserve">
    <value />
  </data>
  <data name="Cardboard boxes" xml:space="preserve">
    <value />
  </data>
  <data name="CarpetCleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Carpets" xml:space="preserve">
    <value />
  </data>
  <data name="Cartons" xml:space="preserve">
    <value />
  </data>
  <data name="Category" xml:space="preserve">
    <value />
  </data>
  <data name="Category (room)" xml:space="preserve">
    <value />
  </data>
  <data name="Cellar" xml:space="preserve">
    <value />
  </data>
  <data name="Change password" xml:space="preserve">
    <value />
  </data>
  <data name="ChangePasswordHint" xml:space="preserve">
    <value />
  </data>
  <data name="Checkout" xml:space="preserve">
    <value />
  </data>
  <data name="CheckoutRedirectMessage" xml:space="preserve">
    <value />
  </data>
  <data name="Choose email content" xml:space="preserve">
    <value />
  </data>
  <data name="ChooseActivity" xml:space="preserve">
    <value />
  </data>
  <data name="CH_Switzerland" xml:space="preserve">
    <value />
  </data>
  <data name="City" xml:space="preserve">
    <value />
  </data>
  <data name="Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="CleaningAndHandover" xml:space="preserve">
    <value />
  </data>
  <data name="CleaningDate" xml:space="preserve">
    <value />
  </data>
  <data name="CleaningType" xml:space="preserve">
    <value />
  </data>
  <data name="Click buttons to filter by activity" xml:space="preserve">
    <value />
  </data>
  <data name="Click to buy" xml:space="preserve">
    <value />
  </data>
  <data name="Company" xml:space="preserve">
    <value />
  </data>
  <data name="Company Data" xml:space="preserve">
    <value />
  </data>
  <data name="Company Move" xml:space="preserve">
    <value />
  </data>
  <data name="Company Name" xml:space="preserve">
    <value />
  </data>
  <data name="CompanyDetails" xml:space="preserve">
    <value />
  </data>
  <data name="CompareAttribute_MustMatch" xml:space="preserve">
    <value />
  </data>
  <data name="CompleteProfileMessage" xml:space="preserve">
    <value />
  </data>
  <data name="Confirm" xml:space="preserve">
    <value />
  </data>
  <data name="Confirm New Password" xml:space="preserve">
    <value />
  </data>
  <data name="Confirm Password" xml:space="preserve">
    <value />
  </data>
  <data name="ConfirmationCode" xml:space="preserve">
    <value />
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value />
  </data>
  <data name="ConfirmMessage" xml:space="preserve">
    <value />
  </data>
  <data name="Construction Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Contact person" xml:space="preserve">
    <value />
  </data>
  <data name="ContactDetails" xml:space="preserve">
    <value />
  </data>
  <data name="ContactSubTitle" xml:space="preserve">
    <value />
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value />
  </data>
  <data name="Create" xml:space="preserve">
    <value />
  </data>
  <data name="Create a room" xml:space="preserve">
    <value />
  </data>
  <data name="Create entry" xml:space="preserve">
    <value />
  </data>
  <data name="Create room content" xml:space="preserve">
    <value />
  </data>
  <data name="Credit card" xml:space="preserve">
    <value />
  </data>
  <data name="Croatian" xml:space="preserve">
    <value />
  </data>
  <data name="Crook" xml:space="preserve">
    <value />
  </data>
  <data name="Currency" xml:space="preserve">
    <value />
  </data>
  <data name="Current Balance" xml:space="preserve">
    <value />
  </data>
  <data name="Current balance: CHF" xml:space="preserve">
    <value />
  </data>
  <data name="Customer" xml:space="preserve">
    <value />
  </data>
  <data name="CustomerFocus" xml:space="preserve">
    <value />
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value />
  </data>
  <data name="Data imported successfully." xml:space="preserve">
    <value />
  </data>
  <data name="Date" xml:space="preserve">
    <value />
  </data>
  <data name="DateFrom" xml:space="preserve">
    <value />
  </data>
  <data name="Daten reinigen" xml:space="preserve">
    <value />
  </data>
  <data name="DateTo" xml:space="preserve">
    <value />
  </data>
  <data name="Delete" xml:space="preserve">
    <value />
  </data>
  <data name="Delete record" xml:space="preserve">
    <value />
  </data>
  <data name="Delete Selected" xml:space="preserve">
    <value />
  </data>
  <data name="Depending on the card type, you will find these in the marked position on the back of the card" xml:space="preserve">
    <value />
  </data>
  <data name="DE_Germany" xml:space="preserve">
    <value />
  </data>
  <data name="Different" xml:space="preserve">
    <value />
  </data>
  <data name="DismantleFurniture" xml:space="preserve">
    <value />
  </data>
  <data name="DismantleLamp" xml:space="preserve">
    <value />
  </data>
  <data name="Disposal" xml:space="preserve">
    <value />
  </data>
  <data name="Distance" xml:space="preserve">
    <value />
  </data>
  <data name="Doors" xml:space="preserve">
    <value />
  </data>
  <data name="DoReset" xml:space="preserve">
    <value />
  </data>
  <data name="Download" xml:space="preserve">
    <value />
  </data>
  <data name="ExportExcel" xml:space="preserve">
    <value />
  </data>
  <data name="Edit" xml:space="preserve">
    <value />
  </data>
  <data name="Edit archive data" xml:space="preserve">
    <value />
  </data>
  <data name="Edit category (rooms)" xml:space="preserve">
    <value />
  </data>
  <data name="Edit entry" xml:space="preserve">
    <value />
  </data>
  <data name="Edit messages" xml:space="preserve">
    <value />
  </data>
  <data name="Edit objects" xml:space="preserve">
    <value />
  </data>
  <data name="Edit partner profile" xml:space="preserve">
    <value />
  </data>
  <data name="Edit Profile Data" xml:space="preserve">
    <value />
  </data>
  <data name="Edit room content" xml:space="preserve">
    <value />
  </data>
  <data name="efh" xml:space="preserve">
    <value />
  </data>
  <data name="EightFloor" xml:space="preserve">
    <value />
  </data>
  <data name="Einzug" xml:space="preserve">
    <value />
  </data>
  <data name="Electrician" xml:space="preserve">
    <value />
  </data>
  <data name="Email" xml:space="preserve">
    <value />
  </data>
  <data name="Email confirm" xml:space="preserve">
    <value />
  </data>
  <data name="Email senden" xml:space="preserve">
    <value />
  </data>
  <data name="Email texts" xml:space="preserve">
    <value />
  </data>
  <data name="EmailAddressAttribute_ValidationError" xml:space="preserve">
    <value />
  </data>
  <data name="EmailAlreadyExistsError" xml:space="preserve">
    <value />
  </data>
  <data name="EmailConfirmationMessage1" xml:space="preserve">
    <value />
  </data>
  <data name="EmailConfirmationMessage2" xml:space="preserve">
    <value />
  </data>
  <data name="EmailConfirmationMessage3" xml:space="preserve">
    <value />
  </data>
  <data name="EmailConfirmationMessage4" xml:space="preserve">
    <value />
  </data>
  <data name="EmailConfirmationMessage5" xml:space="preserve">
    <value />
  </data>
  <data name="EmailSenden" xml:space="preserve">
    <value />
  </data>
  <data name="EmailSentSuccessfully" xml:space="preserve">
    <value />
  </data>
  <data name="EmailToCustomer" xml:space="preserve">
    <value />
  </data>
  <data name="EmptyOrdersMessage" xml:space="preserve">
    <value />
  </data>
  <data name="EmptyOrdersTitle" xml:space="preserve">
    <value />
  </data>
  <data name="EmptyPurchasedOrders" xml:space="preserve">
    <value />
  </data>
  <data name="English" xml:space="preserve">
    <value />
  </data>
  <data name="Enter the ID number. a" xml:space="preserve">
    <value />
  </data>
  <data name="EnterCompanyName" xml:space="preserve">
    <value />
  </data>
  <data name="EnterYourEmail" xml:space="preserve">
    <value />
  </data>
  <data name="Evaluation" xml:space="preserve">
    <value />
  </data>
  <data name="Event Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Excellent" xml:space="preserve">
    <value />
  </data>
  <data name="Excerpt from" xml:space="preserve">
    <value />
  </data>
  <data name="Exec. Date" xml:space="preserve">
    <value />
  </data>
  <data name="ExecDate" xml:space="preserve">
    <value />
  </data>
  <data name="ExecutionDate" xml:space="preserve">
    <value />
  </data>
  <data name="Expiry Date" xml:space="preserve">
    <value />
  </data>
  <data name="External" xml:space="preserve">
    <value />
  </data>
  <data name="Final Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="FiveFloor" xml:space="preserve">
    <value />
  </data>
  <data name="Flexibility" xml:space="preserve">
    <value />
  </data>
  <data name="Flexible" xml:space="preserve">
    <value />
  </data>
  <data name="Floor" xml:space="preserve">
    <value />
  </data>
  <data name="Floor Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="FloorAndPanels" xml:space="preserve">
    <value />
  </data>
  <data name="Focus" xml:space="preserve">
    <value />
  </data>
  <data name="ForbiddenMessage" xml:space="preserve">
    <value />
  </data>
  <data name="ForbiddenTitle" xml:space="preserve">
    <value />
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value />
  </data>
  <data name="ForgotPasswordMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FourFloor" xml:space="preserve">
    <value />
  </data>
  <data name="French" xml:space="preserve">
    <value />
  </data>
  <data name="From" xml:space="preserve">
    <value />
  </data>
  <data name="Furniture" xml:space="preserve">
    <value />
  </data>
  <data name="FurnitureAssembly" xml:space="preserve">
    <value />
  </data>
  <data name="FurnitureLift" xml:space="preserve">
    <value />
  </data>
  <data name="Garage" xml:space="preserve">
    <value />
  </data>
  <data name="Garden" xml:space="preserve">
    <value />
  </data>
  <data name="Gastronomy" xml:space="preserve">
    <value />
  </data>
  <data name="General statistics" xml:space="preserve">
    <value />
  </data>
  <data name="German" xml:space="preserve">
    <value />
  </data>
  <data name="Gisper" xml:space="preserve">
    <value />
  </data>
  <data name="Good" xml:space="preserve">
    <value />
  </data>
  <data name="GreaterTenFloor" xml:space="preserve">
    <value />
  </data>
  <data name="HandOverDate" xml:space="preserve">
    <value />
  </data>
  <data name="HasInventory" xml:space="preserve">
    <value />
  </data>
  <data name="HaveAccount" xml:space="preserve">
    <value />
  </data>
  <data name="Healthcare" xml:space="preserve">
    <value />
  </data>
  <data name="HeatingAndEnergy" xml:space="preserve">
    <value />
  </data>
  <data name="HeavyLoad" xml:space="preserve">
    <value />
  </data>
  <data name="Hello" xml:space="preserve">
    <value />
  </data>
  <data name="HighPressure" xml:space="preserve">
    <value />
  </data>
  <data name="Hotel Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Hotel industry" xml:space="preserve">
    <value />
  </data>
  <data name="House Keeping" xml:space="preserve">
    <value />
  </data>
  <data name="House maintenance" xml:space="preserve">
    <value />
  </data>
  <data name="I have read and agree to the general terms and conditions for purchasing inquiries online via TaskDotNets" xml:space="preserve">
    <value />
  </data>
  <data name="IBAN" xml:space="preserve">
    <value />
  </data>
  <data name="ID Number" xml:space="preserve">
    <value />
  </data>
  <data name="Image" xml:space="preserve">
    <value />
  </data>
  <data name="IndividualActivity" xml:space="preserve">
    <value />
  </data>
  <data name="Industrial Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Information Sheet" xml:space="preserve">
    <value />
  </data>
  <data name="Inspection" xml:space="preserve">
    <value />
  </data>
  <data name="InstallLamp" xml:space="preserve">
    <value />
  </data>
  <data name="Internal" xml:space="preserve">
    <value />
  </data>
  <data name="Internal/External" xml:space="preserve">
    <value />
  </data>
  <data name="InternalServerErrorMessage" xml:space="preserve">
    <value />
  </data>
  <data name="InternalServerErrorTitle" xml:space="preserve">
    <value />
  </data>
  <data name="International Move" xml:space="preserve">
    <value />
  </data>
  <data name="InvalidAmount" xml:space="preserve">
    <value />
  </data>
  <data name="InvalidInput" xml:space="preserve">
    <value />
  </data>
  <data name="Inventory" xml:space="preserve">
    <value />
  </data>
  <data name="Inventory Items" xml:space="preserve">
    <value />
  </data>
  <data name="Inventory list" xml:space="preserve">
    <value />
  </data>
  <data name="InventoryItem" xml:space="preserve">
    <value />
  </data>
  <data name="Italian" xml:space="preserve">
    <value />
  </data>
  <data name="Items" xml:space="preserve">
    <value />
  </data>
  <data name="ItemsList" xml:space="preserve">
    <value />
  </data>
  <data name="KantonWorkingIn" xml:space="preserve">
    <value />
  </data>
  <data name="KitchenConstruction" xml:space="preserve">
    <value />
  </data>
  <data name="Kurdish" xml:space="preserve">
    <value />
  </data>
  <data name="Laminate" xml:space="preserve">
    <value />
  </data>
  <data name="Lamps" xml:space="preserve">
    <value />
  </data>
  <data name="Land" xml:space="preserve">
    <value />
  </data>
  <data name="Language" xml:space="preserve">
    <value />
  </data>
  <data name="Lift" xml:space="preserve">
    <value />
  </data>
  <data name="Linoleum" xml:space="preserve">
    <value />
  </data>
  <data name="Locksmith" xml:space="preserve">
    <value />
  </data>
  <data name="Log Out" xml:space="preserve">
    <value />
  </data>
  <data name="Login" xml:space="preserve">
    <value />
  </data>
  <data name="loginDesc" xml:space="preserve">
    <value />
  </data>
  <data name="LoginHeader" xml:space="preserve">
    <value />
  </data>
  <data name="Maintenance Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Manage Users" xml:space="preserve">
    <value />
  </data>
  <data name="Maximum offers" xml:space="preserve">
    <value />
  </data>
  <data name="Mechanic" xml:space="preserve">
    <value />
  </data>
  <data name="Message" xml:space="preserve">
    <value />
  </data>
  <data name="Miss" xml:space="preserve">
    <value />
  </data>
  <data name="Mobile" xml:space="preserve">
    <value />
  </data>
  <data name="MoreWork" xml:space="preserve">
    <value />
  </data>
  <data name="Move And Clean" xml:space="preserve">
    <value />
  </data>
  <data name="MoveFrom" xml:space="preserve">
    <value />
  </data>
  <data name="MoveTo" xml:space="preserve">
    <value />
  </data>
  <data name="Moving" xml:space="preserve">
    <value />
  </data>
  <data name="Moving &amp; Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Moving and Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Moving in" xml:space="preserve">
    <value />
  </data>
  <data name="MovingAndCleaning" xml:space="preserve">
    <value />
  </data>
  <data name="MovingBoxes" xml:space="preserve">
    <value />
  </data>
  <data name="Mr" xml:space="preserve">
    <value />
  </data>
  <data name="Mrs" xml:space="preserve">
    <value />
  </data>
  <data name="Name" xml:space="preserve">
    <value />
  </data>
  <data name="Name in English" xml:space="preserve">
    <value />
  </data>
  <data name="Name in French" xml:space="preserve">
    <value />
  </data>
  <data name="Name in German" xml:space="preserve">
    <value />
  </data>
  <data name="Name in Italian" xml:space="preserve">
    <value />
  </data>
  <data name="New" xml:space="preserve">
    <value />
  </data>
  <data name="New entry" xml:space="preserve">
    <value />
  </data>
  <data name="New message" xml:space="preserve">
    <value />
  </data>
  <data name="New Password" xml:space="preserve">
    <value />
  </data>
  <data name="NewPasswordMessage" xml:space="preserve">
    <value />
  </data>
  <data name="NewRequest" xml:space="preserve">
    <value />
  </data>
  <data name="Next" xml:space="preserve">
    <value />
  </data>
  <data name="NineFloor" xml:space="preserve">
    <value />
  </data>
  <data name="No" xml:space="preserve">
    <value />
  </data>
  <data name="NotEnoughBalance" xml:space="preserve">
    <value />
  </data>
  <data name="Notes" xml:space="preserve">
    <value />
  </data>
  <data name="NotFoundMessage" xml:space="preserve">
    <value />
  </data>
  <data name="NotFoundTitle" xml:space="preserve">
    <value />
  </data>
  <data name="Nothing" xml:space="preserve">
    <value />
  </data>
  <data name="NotRequired" xml:space="preserve">
    <value />
  </data>
  <data name="Number" xml:space="preserve">
    <value />
  </data>
  <data name="Object" xml:space="preserve">
    <value />
  </data>
  <data name="Office Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Office Rooms" xml:space="preserve">
    <value />
  </data>
  <data name="Ok" xml:space="preserve">
    <value />
  </data>
  <data name="Old Paassword" xml:space="preserve">
    <value />
  </data>
  <data name="OneDay" xml:space="preserve">
    <value />
  </data>
  <data name="OneFloor" xml:space="preserve">
    <value />
  </data>
  <data name="OneMonth" xml:space="preserve">
    <value />
  </data>
  <data name="OneWeek" xml:space="preserve">
    <value />
  </data>
  <data name="Order-Nr" xml:space="preserve">
    <value />
  </data>
  <data name="OrderCountTitle" xml:space="preserve">
    <value />
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value />
  </data>
  <data name="OrdersList" xml:space="preserve">
    <value />
  </data>
  <data name="OrderType" xml:space="preserve">
    <value />
  </data>
  <data name="OurPartner" xml:space="preserve">
    <value />
  </data>
  <data name="Owner" xml:space="preserve">
    <value />
  </data>
  <data name="Packing" xml:space="preserve">
    <value />
  </data>
  <data name="Pages" xml:space="preserve">
    <value />
  </data>
  <data name="Paid costs CHF" xml:space="preserve">
    <value />
  </data>
  <data name="Paid costs CHF1" xml:space="preserve">
    <value />
  </data>
  <data name="Painting" xml:space="preserve">
    <value />
  </data>
  <data name="Painting &amp; Gipser" xml:space="preserve">
    <value />
  </data>
  <data name="PaintingAndGisper" xml:space="preserve">
    <value />
  </data>
  <data name="Parquet" xml:space="preserve">
    <value />
  </data>
  <data name="Partner Data Updated Successfully" xml:space="preserve">
    <value />
  </data>
  <data name="Partner Profile" xml:space="preserve">
    <value />
  </data>
  <data name="Partner Status" xml:space="preserve">
    <value />
  </data>
  <data name="Partner-No" xml:space="preserve">
    <value />
  </data>
  <data name="PartnerBalanceHistory" xml:space="preserve">
    <value />
  </data>
  <data name="PartnerDataUpdatedSuccessfully" xml:space="preserve">
    <value />
  </data>
  <data name="PartnerList" xml:space="preserve">
    <value />
  </data>
  <data name="PartnerOrders" xml:space="preserve">
    <value />
  </data>
  <data name="PartnerOrdersReport" xml:space="preserve">
    <value />
  </data>
  <data name="Partner_Block_UID" xml:space="preserve">
    <value />
  </data>
  <data name="Partner_Dashboard01" xml:space="preserve">
    <value />
  </data>
  <data name="Partner_Dashboard02" xml:space="preserve">
    <value />
  </data>
  <data name="Partner_Dashboard03" xml:space="preserve">
    <value />
  </data>
  <data name="Password" xml:space="preserve">
    <value />
  </data>
  <data name="Pay CHF" xml:space="preserve">
    <value />
  </data>
  <data name="Pay now" xml:space="preserve">
    <value />
  </data>
  <data name="Payment" xml:space="preserve">
    <value />
  </data>
  <data name="PaymentFehler" xml:space="preserve">
    <value />
  </data>
  <data name="PaymentMethod" xml:space="preserve">
    <value />
  </data>
  <data name="PaymentSuccesfully" xml:space="preserve">
    <value />
  </data>
  <data name="Period" xml:space="preserve">
    <value />
  </data>
  <data name="Phone" xml:space="preserve">
    <value />
  </data>
  <data name="Piano" xml:space="preserve">
    <value />
  </data>
  <data name="Plates" xml:space="preserve">
    <value />
  </data>
  <data name="Please choose a payment method!" xml:space="preserve">
    <value />
  </data>
  <data name="Please pay for the selected order" xml:space="preserve">
    <value />
  </data>
  <data name="PleaseAcceptTerms" xml:space="preserve">
    <value />
  </data>
  <data name="Plumbing" xml:space="preserve">
    <value />
  </data>
  <data name="PName" xml:space="preserve">
    <value />
  </data>
  <data name="Portuguese" xml:space="preserve">
    <value />
  </data>
  <data name="PostBox" xml:space="preserve">
    <value />
  </data>
  <data name="Preisfuer" xml:space="preserve">
    <value />
  </data>
  <data name="Press key to show the list of desired activity" xml:space="preserve">
    <value />
  </data>
  <data name="Price" xml:space="preserve">
    <value />
  </data>
  <data name="Price of the cleaning request" xml:space="preserve">
    <value />
  </data>
  <data name="Price of the combined request" xml:space="preserve">
    <value />
  </data>
  <data name="Price of the moving request" xml:space="preserve">
    <value />
  </data>
  <data name="Price of the painting request" xml:space="preserve">
    <value />
  </data>
  <data name="Price of the plastering request" xml:space="preserve">
    <value />
  </data>
  <data name="Price/Quality" xml:space="preserve">
    <value />
  </data>
  <data name="Print" xml:space="preserve">
    <value />
  </data>
  <data name="PrintReport" xml:space="preserve">
    <value />
  </data>
  <data name="Privacy Policy" xml:space="preserve">
    <value />
  </data>
  <data name="Private Move" xml:space="preserve">
    <value />
  </data>
  <data name="Process an order" xml:space="preserve">
    <value />
  </data>
  <data name="Purchased Activites" xml:space="preserve">
    <value />
  </data>
  <data name="Purchased orders" xml:space="preserve">
    <value />
  </data>
  <data name="PurchaseDate" xml:space="preserve">
    <value />
  </data>
  <data name="Quality" xml:space="preserve">
    <value />
  </data>
  <data name="RangeAttribute_ValidationError" xml:space="preserve">
    <value />
  </data>
  <data name="ReadTermsAndCondotions" xml:space="preserve">
    <value />
  </data>
  <data name="Recharge credit" xml:space="preserve">
    <value />
  </data>
  <data name="RechargeCreditHead1" xml:space="preserve">
    <value />
  </data>
  <data name="RechargeCreditHead2" xml:space="preserve">
    <value />
  </data>
  <data name="RechargeCreditHead3" xml:space="preserve">
    <value />
  </data>
  <data name="RechargeFailedMessage" xml:space="preserve">
    <value />
  </data>
  <data name="RechargeMessage" xml:space="preserve">
    <value />
  </data>
  <data name="RechargeSuccessfullyMessage" xml:space="preserve">
    <value />
  </data>
  <data name="Recurring Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="RefrigerationTechnician" xml:space="preserve">
    <value />
  </data>
  <data name="Register" xml:space="preserve">
    <value />
  </data>
  <data name="Register here" xml:space="preserve">
    <value />
  </data>
  <data name="Register now" xml:space="preserve">
    <value />
  </data>
  <data name="RegisterHeader1" xml:space="preserve">
    <value />
  </data>
  <data name="RegisterHeader2" xml:space="preserve">
    <value />
  </data>
  <data name="RegisterHeader3" xml:space="preserve">
    <value />
  </data>
  <data name="RegisterHeader4" xml:space="preserve">
    <value />
  </data>
  <data name="RegisterHeader5" xml:space="preserve">
    <value />
  </data>
  <data name="Regular Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Remember Me" xml:space="preserve">
    <value />
  </data>
  <data name="Request List" xml:space="preserve">
    <value />
  </data>
  <data name="RequiredAttribute_ValidationError" xml:space="preserve">
    <value />
  </data>
  <data name="ResetDashboard" xml:space="preserve">
    <value />
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value />
  </data>
  <data name="ResetPasswordMessage" xml:space="preserve">
    <value />
  </data>
  <data name="ResetTitle" xml:space="preserve">
    <value />
  </data>
  <data name="Role" xml:space="preserve">
    <value />
  </data>
  <data name="Roofer" xml:space="preserve">
    <value />
  </data>
  <data name="Room" xml:space="preserve">
    <value />
  </data>
  <data name="RoomList" xml:space="preserve">
    <value />
  </data>
  <data name="Rooms" xml:space="preserve">
    <value />
  </data>
  <data name="Russian" xml:space="preserve">
    <value />
  </data>
  <data name="Save" xml:space="preserve">
    <value />
  </data>
  <data name="Save changes" xml:space="preserve">
    <value />
  </data>
  <data name="Select an activity" xml:space="preserve">
    <value />
  </data>
  <data name="Select Branch" xml:space="preserve">
    <value />
  </data>
  <data name="Select Template" xml:space="preserve">
    <value />
  </data>
  <data name="Select the Activity you want" xml:space="preserve">
    <value />
  </data>
  <data name="Select the cantons in which you would like to work here" xml:space="preserve">
    <value />
  </data>
  <data name="Send" xml:space="preserve">
    <value />
  </data>
  <data name="SendEmail" xml:space="preserve">
    <value />
  </data>
  <data name="SendEmailTo" xml:space="preserve">
    <value />
  </data>
  <data name="SendMessageToTheCustomer" xml:space="preserve">
    <value />
  </data>
  <data name="SendResetLink" xml:space="preserve">
    <value />
  </data>
  <data name="Settings" xml:space="preserve">
    <value />
  </data>
  <data name="SevenFloor" xml:space="preserve">
    <value />
  </data>
  <data name="Show all Activity" xml:space="preserve">
    <value />
  </data>
  <data name="Show all Archive data" xml:space="preserve">
    <value />
  </data>
  <data name="Show all Inventory" xml:space="preserve">
    <value />
  </data>
  <data name="Show all Statistics" xml:space="preserve">
    <value />
  </data>
  <data name="Sightseeing" xml:space="preserve">
    <value />
  </data>
  <data name="SixFloor" xml:space="preserve">
    <value />
  </data>
  <data name="SmallTransport" xml:space="preserve">
    <value />
  </data>
  <data name="SoilType" xml:space="preserve">
    <value />
  </data>
  <data name="Sold" xml:space="preserve">
    <value />
  </data>
  <data name="SomeThingWentWrong" xml:space="preserve">
    <value />
  </data>
  <data name="Space" xml:space="preserve">
    <value />
  </data>
  <data name="Spanish" xml:space="preserve">
    <value />
  </data>
  <data name="Start Date" xml:space="preserve">
    <value />
  </data>
  <data name="StartDate" xml:space="preserve">
    <value />
  </data>
  <data name="Statistics" xml:space="preserve">
    <value />
  </data>
  <data name="StatisticsPageTitle" xml:space="preserve">
    <value />
  </data>
  <data name="StatisticsPageTitlePartner" xml:space="preserve">
    <value />
  </data>
  <data name="StatisticsPageTitlePartner2" xml:space="preserve">
    <value />
  </data>
  <data name="Status" xml:space="preserve">
    <value />
  </data>
  <data name="Storage" xml:space="preserve">
    <value />
  </data>
  <data name="Street" xml:space="preserve">
    <value />
  </data>
  <data name="StringLengthAttribute_ValidationError" xml:space="preserve">
    <value />
  </data>
  <data name="Subject" xml:space="preserve">
    <value />
  </data>
  <data name="System setup" xml:space="preserve">
    <value />
  </data>
  <data name="Team" xml:space="preserve">
    <value />
  </data>
  <data name="TenFloor" xml:space="preserve">
    <value />
  </data>
  <data name="TermsNotAccepted" xml:space="preserve">
    <value />
  </data>
  <data name="Thank you for your payment..." xml:space="preserve">
    <value />
  </data>
  <data name="The achievements of partnere for the current month" xml:space="preserve">
    <value />
  </data>
  <data name="The total: CHF" xml:space="preserve">
    <value />
  </data>
  <data name="PartnerDashboardMessage" xml:space="preserve">
    <value />
  </data>
  <data name="This request will be deleted after archiving!" xml:space="preserve">
    <value />
  </data>
  <data name="This table displays the work activity statistics for the current year" xml:space="preserve">
    <value />
  </data>
  <data name="ThisIsRequiredRequest" xml:space="preserve">
    <value />
  </data>
  <data name="ThreeDays" xml:space="preserve">
    <value />
  </data>
  <data name="ThreeFloor" xml:space="preserve">
    <value />
  </data>
  <data name="ThreeWeeks" xml:space="preserve">
    <value />
  </data>
  <data name="Title" xml:space="preserve">
    <value />
  </data>
  <data name="To" xml:space="preserve">
    <value />
  </data>
  <data name="To which branch" xml:space="preserve">
    <value />
  </data>
  <data name="Top Up Balance" xml:space="preserve">
    <value />
  </data>
  <data name="Total volume" xml:space="preserve">
    <value />
  </data>
  <data name="toTheDrainEdge" xml:space="preserve">
    <value />
  </data>
  <data name="toTheLoadingEdge" xml:space="preserve">
    <value />
  </data>
  <data name="Turkish" xml:space="preserve">
    <value />
  </data>
  <data name="TwoDays" xml:space="preserve">
    <value />
  </data>
  <data name="TwoFloor" xml:space="preserve">
    <value />
  </data>
  <data name="TwoWeeks" xml:space="preserve">
    <value />
  </data>
  <data name="UID" xml:space="preserve">
    <value />
  </data>
  <data name="UnauthorizedMessage" xml:space="preserve">
    <value />
  </data>
  <data name="UnauthorizedTitle" xml:space="preserve">
    <value />
  </data>
  <data name="Unit" xml:space="preserve">
    <value />
  </data>
  <data name="Unpacking" xml:space="preserve">
    <value />
  </data>
  <data name="Update" xml:space="preserve">
    <value />
  </data>
  <data name="Upload File" xml:space="preserve">
    <value />
  </data>
  <data name="Useless" xml:space="preserve">
    <value />
  </data>
  <data name="User" xml:space="preserve">
    <value />
  </data>
  <data name="UsersList" xml:space="preserve">
    <value />
  </data>
  <data name="VerifyAccount" xml:space="preserve">
    <value />
  </data>
  <data name="View All" xml:space="preserve">
    <value />
  </data>
  <data name="View order" xml:space="preserve">
    <value />
  </data>
  <data name="Viewing" xml:space="preserve">
    <value />
  </data>
  <data name="ViewingDate" xml:space="preserve">
    <value />
  </data>
  <data name="Volume (m3)" xml:space="preserve">
    <value />
  </data>
  <data name="Volume in m3" xml:space="preserve">
    <value />
  </data>
  <data name="Walls" xml:space="preserve">
    <value />
  </data>
  <data name="WallsAndCeilings" xml:space="preserve">
    <value />
  </data>
  <data name="Washroom" xml:space="preserve">
    <value />
  </data>
  <data name="Weak" xml:space="preserve">
    <value />
  </data>
  <data name="Website" xml:space="preserve">
    <value />
  </data>
  <data name="WelcomeBack" xml:space="preserve">
    <value />
  </data>
  <data name="Welder" xml:space="preserve">
    <value />
  </data>
  <data name="Window Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="Windows" xml:space="preserve">
    <value />
  </data>
  <data name="Windows Cleaning" xml:space="preserve">
    <value />
  </data>
  <data name="WithInventoryList" xml:space="preserve">
    <value />
  </data>
  <data name="Workers" xml:space="preserve">
    <value />
  </data>
  <data name="Workspace" xml:space="preserve">
    <value />
  </data>
  <data name="Yes" xml:space="preserve">
    <value />
  </data>
  <data name="YesOn" xml:space="preserve">
    <value />
  </data>
  <data name="YouWantToPurchaseThisOrder" xml:space="preserve">
    <value />
  </data>
  <data name="AdminDashboardHeader" xml:space="preserve">
    <value />
  </data>
  <data name="AdminDashboardHeader2" xml:space="preserve">
    <value />
  </data>
  <data name="Handyman" xml:space="preserve">
    <value />
  </data>
  <data name="RequestControl" xml:space="preserve">
    <value />
  </data>
  <data name="UpdateUser" xml:space="preserve">
    <value />
  </data>
  <data name="Back" xml:space="preserve">
    <value />
  </data>
  <data name="Credits" xml:space="preserve">
    <value />
  </data>
  <data name="Filter" xml:space="preserve">
    <value />
  </data>
  <data name="NoCreditsFound" xml:space="preserve">
    <value />
  </data>
  <data name="Partnername" xml:space="preserve">
    <value />
  </data>
  <data name="PaymentWay" xml:space="preserve">
    <value />
  </data>
</root>