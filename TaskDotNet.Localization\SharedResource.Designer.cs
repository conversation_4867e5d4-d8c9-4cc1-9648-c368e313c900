﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TaskDotNet.Localization {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class SharedResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal SharedResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("TaskDotNet.Localization.SharedResource", typeof(SharedResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Account {
            get {
                return ResourceManager.GetString("Account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Account_Data {
            get {
                return ResourceManager.GetString("Account Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Account_Settings {
            get {
                return ResourceManager.GetString("Account Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Actions {
            get {
                return ResourceManager.GetString("Actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Active {
            get {
                return ResourceManager.GetString("Active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Activites_setup {
            get {
                return ResourceManager.GetString("Activites setup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Activities_List {
            get {
                return ResourceManager.GetString("Activities List", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Activity {
            get {
                return ResourceManager.GetString("Activity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ActivityDetails {
            get {
                return ResourceManager.GetString("ActivityDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ActivityDetailsTitle {
            get {
                return ResourceManager.GetString("ActivityDetailsTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Add_to_archive {
            get {
                return ResourceManager.GetString("Add to archive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string AddAmount {
            get {
                return ResourceManager.GetString("AddAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string AdditionalServices {
            get {
                return ResourceManager.GetString("AdditionalServices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Additive {
            get {
                return ResourceManager.GetString("Additive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string AdminDashboardHeader {
            get {
                return ResourceManager.GetString("AdminDashboardHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string AdminDashboardHeader2 {
            get {
                return ResourceManager.GetString("AdminDashboardHeader2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Administrator {
            get {
                return ResourceManager.GetString("Administrator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string AdvertisingList {
            get {
                return ResourceManager.GetString("AdvertisingList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Albanian {
            get {
                return ResourceManager.GetString("Albanian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string All {
            get {
                return ResourceManager.GetString("All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string All_Activity {
            get {
                return ResourceManager.GetString("All Activity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string All_Paertners {
            get {
                return ResourceManager.GetString("All Paertners", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string AlreadyPurchased {
            get {
                return ResourceManager.GetString("AlreadyPurchased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Amount {
            get {
                return ResourceManager.GetString("Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string AmountMustBeGreaterThanZero {
            get {
                return ResourceManager.GetString("AmountMustBeGreaterThanZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Apartment {
            get {
                return ResourceManager.GetString("Apartment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Approx_ {
            get {
                return ResourceManager.GetString("Approx.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Arabic {
            get {
                return ResourceManager.GetString("Arabic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Archive {
            get {
                return ResourceManager.GetString("Archive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Archive_Content {
            get {
                return ResourceManager.GetString("Archive Content", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Archive_entry {
            get {
                return ResourceManager.GetString("Archive entry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Are_you_sure_you_want_to_delete_the_selected_entry_ {
            get {
                return ResourceManager.GetString("Are you sure you want to delete the selected entry?", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Area {
            get {
                return ResourceManager.GetString("Area", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string AreYouSure {
            get {
                return ResourceManager.GetString("AreYouSure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string AssembleFurniture {
            get {
                return ResourceManager.GetString("AssembleFurniture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string AT_Austria {
            get {
                return ResourceManager.GetString("AT_Austria", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Auszug {
            get {
                return ResourceManager.GetString("Auszug", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Back {
            get {
                return ResourceManager.GetString("Back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Background {
            get {
                return ResourceManager.GetString("Background", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string BackToList {
            get {
                return ResourceManager.GetString("BackToList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string BadRequestMessage {
            get {
                return ResourceManager.GetString("BadRequestMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string BadRequestTitle {
            get {
                return ResourceManager.GetString("BadRequestTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Balance {
            get {
                return ResourceManager.GetString("Balance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string BalanceRechargeHistory {
            get {
                return ResourceManager.GetString("BalanceRechargeHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Balcony {
            get {
                return ResourceManager.GetString("Balcony", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Bank {
            get {
                return ResourceManager.GetString("Bank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Bank_Name {
            get {
                return ResourceManager.GetString("Bank Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Basement__Cellar {
            get {
                return ResourceManager.GetString("Basement, Cellar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Basic_Cleaning {
            get {
                return ResourceManager.GetString("Basic Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Blocked {
            get {
                return ResourceManager.GetString("Blocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Both {
            get {
                return ResourceManager.GetString("Both", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string BoxCity {
            get {
                return ResourceManager.GetString("BoxCity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Boxes {
            get {
                return ResourceManager.GetString("Boxes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Branch {
            get {
                return ResourceManager.GetString("Branch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Building_Cleaning {
            get {
                return ResourceManager.GetString("Building Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Business {
            get {
                return ResourceManager.GetString("Business", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Card_number {
            get {
                return ResourceManager.GetString("Card number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Cardboard_boxes {
            get {
                return ResourceManager.GetString("Cardboard boxes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string CarpetCleaning {
            get {
                return ResourceManager.GetString("CarpetCleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Carpets {
            get {
                return ResourceManager.GetString("Carpets", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Cartons {
            get {
                return ResourceManager.GetString("Cartons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Category {
            get {
                return ResourceManager.GetString("Category", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Category__room_ {
            get {
                return ResourceManager.GetString("Category (room)", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Cellar {
            get {
                return ResourceManager.GetString("Cellar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string CH_Switzerland {
            get {
                return ResourceManager.GetString("CH_Switzerland", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Change_password {
            get {
                return ResourceManager.GetString("Change password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ChangePasswordHint {
            get {
                return ResourceManager.GetString("ChangePasswordHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Checkout {
            get {
                return ResourceManager.GetString("Checkout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string CheckoutRedirectMessage {
            get {
                return ResourceManager.GetString("CheckoutRedirectMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Choose_email_content {
            get {
                return ResourceManager.GetString("Choose email content", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ChooseActivity {
            get {
                return ResourceManager.GetString("ChooseActivity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Cleaning {
            get {
                return ResourceManager.GetString("Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string CleaningAndHandover {
            get {
                return ResourceManager.GetString("CleaningAndHandover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string CleaningDate {
            get {
                return ResourceManager.GetString("CleaningDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string CleaningType {
            get {
                return ResourceManager.GetString("CleaningType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Click_buttons_to_filter_by_activity {
            get {
                return ResourceManager.GetString("Click buttons to filter by activity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Click_to_buy {
            get {
                return ResourceManager.GetString("Click to buy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Company {
            get {
                return ResourceManager.GetString("Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Company_Data {
            get {
                return ResourceManager.GetString("Company Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Company_Move {
            get {
                return ResourceManager.GetString("Company Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Company_Name {
            get {
                return ResourceManager.GetString("Company Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string CompanyDetails {
            get {
                return ResourceManager.GetString("CompanyDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string CompareAttribute_MustMatch {
            get {
                return ResourceManager.GetString("CompareAttribute_MustMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string CompleteProfileMessage {
            get {
                return ResourceManager.GetString("CompleteProfileMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Confirm {
            get {
                return ResourceManager.GetString("Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Confirm_New_Password {
            get {
                return ResourceManager.GetString("Confirm New Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Confirm_Password {
            get {
                return ResourceManager.GetString("Confirm Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ConfirmationCode {
            get {
                return ResourceManager.GetString("ConfirmationCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Confirmed {
            get {
                return ResourceManager.GetString("Confirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ConfirmMessage {
            get {
                return ResourceManager.GetString("ConfirmMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Construction_Cleaning {
            get {
                return ResourceManager.GetString("Construction Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Contact_person {
            get {
                return ResourceManager.GetString("Contact person", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ContactDetails {
            get {
                return ResourceManager.GetString("ContactDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ContactSubTitle {
            get {
                return ResourceManager.GetString("ContactSubTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ContactUs {
            get {
                return ResourceManager.GetString("ContactUs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Create {
            get {
                return ResourceManager.GetString("Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Create_a_room {
            get {
                return ResourceManager.GetString("Create a room", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Create_entry {
            get {
                return ResourceManager.GetString("Create entry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Create_room_content {
            get {
                return ResourceManager.GetString("Create room content", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Credit_card {
            get {
                return ResourceManager.GetString("Credit card", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Credits {
            get {
                return ResourceManager.GetString("Credits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Croatian {
            get {
                return ResourceManager.GetString("Croatian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Crook {
            get {
                return ResourceManager.GetString("Crook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Currency {
            get {
                return ResourceManager.GetString("Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Current_Balance {
            get {
                return ResourceManager.GetString("Current Balance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Current_balance__CHF {
            get {
                return ResourceManager.GetString("Current balance: CHF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Customer {
            get {
                return ResourceManager.GetString("Customer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string CustomerFocus {
            get {
                return ResourceManager.GetString("CustomerFocus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Dashboard {
            get {
                return ResourceManager.GetString("Dashboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Data_imported_successfully_ {
            get {
                return ResourceManager.GetString("Data imported successfully.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string DateFrom {
            get {
                return ResourceManager.GetString("DateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Daten_reinigen {
            get {
                return ResourceManager.GetString("Daten reinigen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string DateTo {
            get {
                return ResourceManager.GetString("DateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string DE_Germany {
            get {
                return ResourceManager.GetString("DE_Germany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Delete_record {
            get {
                return ResourceManager.GetString("Delete record", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Delete_Selected {
            get {
                return ResourceManager.GetString("Delete Selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Depending_on_the_card_type__you_will_find_these_in_the_marked_position_on_the_back_of_the_card {
            get {
                return ResourceManager.GetString("Depending on the card type, you will find these in the marked position on the bac" +
                        "k of the card", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Different {
            get {
                return ResourceManager.GetString("Different", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string DismantleFurniture {
            get {
                return ResourceManager.GetString("DismantleFurniture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string DismantleLamp {
            get {
                return ResourceManager.GetString("DismantleLamp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Disposal {
            get {
                return ResourceManager.GetString("Disposal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Distance {
            get {
                return ResourceManager.GetString("Distance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Doors {
            get {
                return ResourceManager.GetString("Doors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string DoReset {
            get {
                return ResourceManager.GetString("DoReset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Download {
            get {
                return ResourceManager.GetString("Download", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Edit_archive_data {
            get {
                return ResourceManager.GetString("Edit archive data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Edit_category__rooms_ {
            get {
                return ResourceManager.GetString("Edit category (rooms)", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Edit_entry {
            get {
                return ResourceManager.GetString("Edit entry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Edit_messages {
            get {
                return ResourceManager.GetString("Edit messages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Edit_objects {
            get {
                return ResourceManager.GetString("Edit objects", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Edit_partner_profile {
            get {
                return ResourceManager.GetString("Edit partner profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Edit_Profile_Data {
            get {
                return ResourceManager.GetString("Edit Profile Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Edit_room_content {
            get {
                return ResourceManager.GetString("Edit room content", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string efh {
            get {
                return ResourceManager.GetString("efh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EightFloor {
            get {
                return ResourceManager.GetString("EightFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Einzug {
            get {
                return ResourceManager.GetString("Einzug", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Electrician {
            get {
                return ResourceManager.GetString("Electrician", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Email_confirm {
            get {
                return ResourceManager.GetString("Email confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Email_senden {
            get {
                return ResourceManager.GetString("Email senden", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Email_texts {
            get {
                return ResourceManager.GetString("Email texts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmailAddressAttribute_ValidationError {
            get {
                return ResourceManager.GetString("EmailAddressAttribute_ValidationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmailAlreadyExistsError {
            get {
                return ResourceManager.GetString("EmailAlreadyExistsError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmailConfirmationMessage1 {
            get {
                return ResourceManager.GetString("EmailConfirmationMessage1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmailConfirmationMessage2 {
            get {
                return ResourceManager.GetString("EmailConfirmationMessage2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmailConfirmationMessage3 {
            get {
                return ResourceManager.GetString("EmailConfirmationMessage3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmailConfirmationMessage4 {
            get {
                return ResourceManager.GetString("EmailConfirmationMessage4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmailConfirmationMessage5 {
            get {
                return ResourceManager.GetString("EmailConfirmationMessage5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmailSenden {
            get {
                return ResourceManager.GetString("EmailSenden", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmailSentSuccessfully {
            get {
                return ResourceManager.GetString("EmailSentSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmailToCustomer {
            get {
                return ResourceManager.GetString("EmailToCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmptyOrdersMessage {
            get {
                return ResourceManager.GetString("EmptyOrdersMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmptyOrdersTitle {
            get {
                return ResourceManager.GetString("EmptyOrdersTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmptyPurchasedOrders {
            get {
                return ResourceManager.GetString("EmptyPurchasedOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string English {
            get {
                return ResourceManager.GetString("English", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Enter_the_ID_number__a {
            get {
                return ResourceManager.GetString("Enter the ID number. a", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EnterCompanyName {
            get {
                return ResourceManager.GetString("EnterCompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EnterYourEmail {
            get {
                return ResourceManager.GetString("EnterYourEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Evaluation {
            get {
                return ResourceManager.GetString("Evaluation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Event_Cleaning {
            get {
                return ResourceManager.GetString("Event Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Excellent {
            get {
                return ResourceManager.GetString("Excellent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Excerpt_from {
            get {
                return ResourceManager.GetString("Excerpt from", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Exec__Date {
            get {
                return ResourceManager.GetString("Exec. Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ExecDate {
            get {
                return ResourceManager.GetString("ExecDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ExecutionDate {
            get {
                return ResourceManager.GetString("ExecutionDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Expiry_Date {
            get {
                return ResourceManager.GetString("Expiry Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ExportExcel {
            get {
                return ResourceManager.GetString("ExportExcel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string External {
            get {
                return ResourceManager.GetString("External", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Filter {
            get {
                return ResourceManager.GetString("Filter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Final_Cleaning {
            get {
                return ResourceManager.GetString("Final Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string FiveFloor {
            get {
                return ResourceManager.GetString("FiveFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Flexibility {
            get {
                return ResourceManager.GetString("Flexibility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Flexible {
            get {
                return ResourceManager.GetString("Flexible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Floor {
            get {
                return ResourceManager.GetString("Floor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Floor_Cleaning {
            get {
                return ResourceManager.GetString("Floor Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string FloorAndPanels {
            get {
                return ResourceManager.GetString("FloorAndPanels", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Focus {
            get {
                return ResourceManager.GetString("Focus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ForbiddenMessage {
            get {
                return ResourceManager.GetString("ForbiddenMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ForbiddenTitle {
            get {
                return ResourceManager.GetString("ForbiddenTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ForgotPassword {
            get {
                return ResourceManager.GetString("ForgotPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ForgotPasswordMessage {
            get {
                return ResourceManager.GetString("ForgotPasswordMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string FourFloor {
            get {
                return ResourceManager.GetString("FourFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string French {
            get {
                return ResourceManager.GetString("French", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string From {
            get {
                return ResourceManager.GetString("From", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Furniture {
            get {
                return ResourceManager.GetString("Furniture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string FurnitureAssembly {
            get {
                return ResourceManager.GetString("FurnitureAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string FurnitureLift {
            get {
                return ResourceManager.GetString("FurnitureLift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Garage {
            get {
                return ResourceManager.GetString("Garage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Garden {
            get {
                return ResourceManager.GetString("Garden", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Gastronomy {
            get {
                return ResourceManager.GetString("Gastronomy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string General_statistics {
            get {
                return ResourceManager.GetString("General statistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string German {
            get {
                return ResourceManager.GetString("German", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Gisper {
            get {
                return ResourceManager.GetString("Gisper", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Good {
            get {
                return ResourceManager.GetString("Good", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string GreaterTenFloor {
            get {
                return ResourceManager.GetString("GreaterTenFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string HandOverDate {
            get {
                return ResourceManager.GetString("HandOverDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Handyman {
            get {
                return ResourceManager.GetString("Handyman", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string HasInventory {
            get {
                return ResourceManager.GetString("HasInventory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string HaveAccount {
            get {
                return ResourceManager.GetString("HaveAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Healthcare {
            get {
                return ResourceManager.GetString("Healthcare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string HeatingAndEnergy {
            get {
                return ResourceManager.GetString("HeatingAndEnergy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string HeavyLoad {
            get {
                return ResourceManager.GetString("HeavyLoad", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Hello {
            get {
                return ResourceManager.GetString("Hello", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string HighPressure {
            get {
                return ResourceManager.GetString("HighPressure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Hotel_Cleaning {
            get {
                return ResourceManager.GetString("Hotel Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Hotel_industry {
            get {
                return ResourceManager.GetString("Hotel industry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string House_Keeping {
            get {
                return ResourceManager.GetString("House Keeping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string House_maintenance {
            get {
                return ResourceManager.GetString("House maintenance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string I_have_read_and_agree_to_the_general_terms_and_conditions_for_purchasing_inquiries_online_via_TaskDotNets {
            get {
                return ResourceManager.GetString("I have read and agree to the general terms and conditions for purchasing inquirie" +
                        "s online via TaskDotNets", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string IBAN {
            get {
                return ResourceManager.GetString("IBAN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ID_Number {
            get {
                return ResourceManager.GetString("ID Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Image {
            get {
                return ResourceManager.GetString("Image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string IndividualActivity {
            get {
                return ResourceManager.GetString("IndividualActivity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Industrial_Cleaning {
            get {
                return ResourceManager.GetString("Industrial Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Information_Sheet {
            get {
                return ResourceManager.GetString("Information Sheet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Inspection {
            get {
                return ResourceManager.GetString("Inspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string InstallLamp {
            get {
                return ResourceManager.GetString("InstallLamp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Internal {
            get {
                return ResourceManager.GetString("Internal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Internal_External {
            get {
                return ResourceManager.GetString("Internal/External", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string InternalServerErrorMessage {
            get {
                return ResourceManager.GetString("InternalServerErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string InternalServerErrorTitle {
            get {
                return ResourceManager.GetString("InternalServerErrorTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string International_Move {
            get {
                return ResourceManager.GetString("International Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string InvalidAmount {
            get {
                return ResourceManager.GetString("InvalidAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string InvalidInput {
            get {
                return ResourceManager.GetString("InvalidInput", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Inventory {
            get {
                return ResourceManager.GetString("Inventory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Inventory_Items {
            get {
                return ResourceManager.GetString("Inventory Items", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Inventory_list {
            get {
                return ResourceManager.GetString("Inventory list", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string InventoryItem {
            get {
                return ResourceManager.GetString("InventoryItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Italian {
            get {
                return ResourceManager.GetString("Italian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Items {
            get {
                return ResourceManager.GetString("Items", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ItemsList {
            get {
                return ResourceManager.GetString("ItemsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string KantonWorkingIn {
            get {
                return ResourceManager.GetString("KantonWorkingIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string KitchenConstruction {
            get {
                return ResourceManager.GetString("KitchenConstruction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Kurdish {
            get {
                return ResourceManager.GetString("Kurdish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Laminate {
            get {
                return ResourceManager.GetString("Laminate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Lamps {
            get {
                return ResourceManager.GetString("Lamps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Land {
            get {
                return ResourceManager.GetString("Land", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Lift {
            get {
                return ResourceManager.GetString("Lift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Linoleum {
            get {
                return ResourceManager.GetString("Linoleum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Locksmith {
            get {
                return ResourceManager.GetString("Locksmith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Log_Out {
            get {
                return ResourceManager.GetString("Log Out", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string loginDesc {
            get {
                return ResourceManager.GetString("loginDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string LoginHeader {
            get {
                return ResourceManager.GetString("LoginHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Maintenance_Cleaning {
            get {
                return ResourceManager.GetString("Maintenance Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Manage_Users {
            get {
                return ResourceManager.GetString("Manage Users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Maximum_offers {
            get {
                return ResourceManager.GetString("Maximum offers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Mechanic {
            get {
                return ResourceManager.GetString("Mechanic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Message {
            get {
                return ResourceManager.GetString("Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Miss {
            get {
                return ResourceManager.GetString("Miss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Mobile {
            get {
                return ResourceManager.GetString("Mobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string MoreWork {
            get {
                return ResourceManager.GetString("MoreWork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Move_And_Clean {
            get {
                return ResourceManager.GetString("Move And Clean", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string MoveFrom {
            get {
                return ResourceManager.GetString("MoveFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string MoveTo {
            get {
                return ResourceManager.GetString("MoveTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Moving {
            get {
                return ResourceManager.GetString("Moving", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Moving___Cleaning {
            get {
                return ResourceManager.GetString("Moving & Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Moving_and_Cleaning {
            get {
                return ResourceManager.GetString("Moving and Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Moving_in {
            get {
                return ResourceManager.GetString("Moving in", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string MovingAndCleaning {
            get {
                return ResourceManager.GetString("MovingAndCleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string MovingBoxes {
            get {
                return ResourceManager.GetString("MovingBoxes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Mr {
            get {
                return ResourceManager.GetString("Mr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Mrs {
            get {
                return ResourceManager.GetString("Mrs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Name_in_English {
            get {
                return ResourceManager.GetString("Name in English", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Name_in_French {
            get {
                return ResourceManager.GetString("Name in French", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Name_in_German {
            get {
                return ResourceManager.GetString("Name in German", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Name_in_Italian {
            get {
                return ResourceManager.GetString("Name in Italian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string New {
            get {
                return ResourceManager.GetString("New", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string New_entry {
            get {
                return ResourceManager.GetString("New entry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string New_message {
            get {
                return ResourceManager.GetString("New message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string New_Password {
            get {
                return ResourceManager.GetString("New Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string NewPasswordMessage {
            get {
                return ResourceManager.GetString("NewPasswordMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string NewRequest {
            get {
                return ResourceManager.GetString("NewRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Next {
            get {
                return ResourceManager.GetString("Next", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string NineFloor {
            get {
                return ResourceManager.GetString("NineFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string NoCreditsFound {
            get {
                return ResourceManager.GetString("NoCreditsFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string NotEnoughBalance {
            get {
                return ResourceManager.GetString("NotEnoughBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string NotFoundMessage {
            get {
                return ResourceManager.GetString("NotFoundMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string NotFoundTitle {
            get {
                return ResourceManager.GetString("NotFoundTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Nothing {
            get {
                return ResourceManager.GetString("Nothing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string NotRequired {
            get {
                return ResourceManager.GetString("NotRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Number {
            get {
                return ResourceManager.GetString("Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Object {
            get {
                return ResourceManager.GetString("Object", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Office_Cleaning {
            get {
                return ResourceManager.GetString("Office Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Office_Rooms {
            get {
                return ResourceManager.GetString("Office Rooms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Ok {
            get {
                return ResourceManager.GetString("Ok", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Old_Paassword {
            get {
                return ResourceManager.GetString("Old Paassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OneDay {
            get {
                return ResourceManager.GetString("OneDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OneFloor {
            get {
                return ResourceManager.GetString("OneFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OneMonth {
            get {
                return ResourceManager.GetString("OneMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OneWeek {
            get {
                return ResourceManager.GetString("OneWeek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Order_Nr {
            get {
                return ResourceManager.GetString("Order-Nr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderCountTitle {
            get {
                return ResourceManager.GetString("OrderCountTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderNumber {
            get {
                return ResourceManager.GetString("OrderNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrdersList {
            get {
                return ResourceManager.GetString("OrdersList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OrderType {
            get {
                return ResourceManager.GetString("OrderType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string OurPartner {
            get {
                return ResourceManager.GetString("OurPartner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Owner {
            get {
                return ResourceManager.GetString("Owner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Packing {
            get {
                return ResourceManager.GetString("Packing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Pages {
            get {
                return ResourceManager.GetString("Pages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Paid_costs_CHF {
            get {
                return ResourceManager.GetString("Paid costs CHF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Paid_costs_CHF1 {
            get {
                return ResourceManager.GetString("Paid costs CHF1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Painting {
            get {
                return ResourceManager.GetString("Painting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Painting___Gipser {
            get {
                return ResourceManager.GetString("Painting & Gipser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PaintingAndGisper {
            get {
                return ResourceManager.GetString("PaintingAndGisper", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Parquet {
            get {
                return ResourceManager.GetString("Parquet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Partner_Block_UID {
            get {
                return ResourceManager.GetString("Partner_Block_UID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Partner_Dashboard01 {
            get {
                return ResourceManager.GetString("Partner_Dashboard01", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Partner_Dashboard02 {
            get {
                return ResourceManager.GetString("Partner_Dashboard02", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Partner_Dashboard03 {
            get {
                return ResourceManager.GetString("Partner_Dashboard03", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Partner_Data_Updated_Successfully {
            get {
                return ResourceManager.GetString("Partner Data Updated Successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Partner_No {
            get {
                return ResourceManager.GetString("Partner-No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Partner_Profile {
            get {
                return ResourceManager.GetString("Partner Profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Partner_Status {
            get {
                return ResourceManager.GetString("Partner Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PartnerBalanceHistory {
            get {
                return ResourceManager.GetString("PartnerBalanceHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PartnerDashboardMessage {
            get {
                return ResourceManager.GetString("PartnerDashboardMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PartnerDataUpdatedSuccessfully {
            get {
                return ResourceManager.GetString("PartnerDataUpdatedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PartnerList {
            get {
                return ResourceManager.GetString("PartnerList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Partnername {
            get {
                return ResourceManager.GetString("Partnername", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PartnerOrders {
            get {
                return ResourceManager.GetString("PartnerOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PartnerOrdersReport {
            get {
                return ResourceManager.GetString("PartnerOrdersReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Pay_CHF {
            get {
                return ResourceManager.GetString("Pay CHF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Pay_now {
            get {
                return ResourceManager.GetString("Pay now", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Payment {
            get {
                return ResourceManager.GetString("Payment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PaymentFehler {
            get {
                return ResourceManager.GetString("PaymentFehler", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PaymentMethod {
            get {
                return ResourceManager.GetString("PaymentMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PaymentSuccesfully {
            get {
                return ResourceManager.GetString("PaymentSuccesfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PaymentWay {
            get {
                return ResourceManager.GetString("PaymentWay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Period {
            get {
                return ResourceManager.GetString("Period", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Phone {
            get {
                return ResourceManager.GetString("Phone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Piano {
            get {
                return ResourceManager.GetString("Piano", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Plates {
            get {
                return ResourceManager.GetString("Plates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Please_choose_a_payment_method_ {
            get {
                return ResourceManager.GetString("Please choose a payment method!", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Please_pay_for_the_selected_order {
            get {
                return ResourceManager.GetString("Please pay for the selected order", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PleaseAcceptTerms {
            get {
                return ResourceManager.GetString("PleaseAcceptTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Plumbing {
            get {
                return ResourceManager.GetString("Plumbing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PName {
            get {
                return ResourceManager.GetString("PName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Portuguese {
            get {
                return ResourceManager.GetString("Portuguese", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PostBox {
            get {
                return ResourceManager.GetString("PostBox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Preisfuer {
            get {
                return ResourceManager.GetString("Preisfuer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Press_key_to_show_the_list_of_desired_activity {
            get {
                return ResourceManager.GetString("Press key to show the list of desired activity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Price {
            get {
                return ResourceManager.GetString("Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Price_of_the_cleaning_request {
            get {
                return ResourceManager.GetString("Price of the cleaning request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Price_of_the_combined_request {
            get {
                return ResourceManager.GetString("Price of the combined request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Price_of_the_moving_request {
            get {
                return ResourceManager.GetString("Price of the moving request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Price_of_the_painting_request {
            get {
                return ResourceManager.GetString("Price of the painting request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Price_of_the_plastering_request {
            get {
                return ResourceManager.GetString("Price of the plastering request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Price_Quality {
            get {
                return ResourceManager.GetString("Price/Quality", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Print {
            get {
                return ResourceManager.GetString("Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PrintReport {
            get {
                return ResourceManager.GetString("PrintReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Privacy_Policy {
            get {
                return ResourceManager.GetString("Privacy Policy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Private_Move {
            get {
                return ResourceManager.GetString("Private Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Process_an_order {
            get {
                return ResourceManager.GetString("Process an order", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Purchased_Activites {
            get {
                return ResourceManager.GetString("Purchased Activites", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Purchased_orders {
            get {
                return ResourceManager.GetString("Purchased orders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PurchaseDate {
            get {
                return ResourceManager.GetString("PurchaseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Quality {
            get {
                return ResourceManager.GetString("Quality", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RangeAttribute_ValidationError {
            get {
                return ResourceManager.GetString("RangeAttribute_ValidationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ReadTermsAndCondotions {
            get {
                return ResourceManager.GetString("ReadTermsAndCondotions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Recharge_credit {
            get {
                return ResourceManager.GetString("Recharge credit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RechargeCreditHead1 {
            get {
                return ResourceManager.GetString("RechargeCreditHead1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RechargeCreditHead2 {
            get {
                return ResourceManager.GetString("RechargeCreditHead2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RechargeCreditHead3 {
            get {
                return ResourceManager.GetString("RechargeCreditHead3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RechargeFailedMessage {
            get {
                return ResourceManager.GetString("RechargeFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RechargeMessage {
            get {
                return ResourceManager.GetString("RechargeMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RechargeSuccessfullyMessage {
            get {
                return ResourceManager.GetString("RechargeSuccessfullyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Recurring_Cleaning {
            get {
                return ResourceManager.GetString("Recurring Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RefrigerationTechnician {
            get {
                return ResourceManager.GetString("RefrigerationTechnician", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Register {
            get {
                return ResourceManager.GetString("Register", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Register_here {
            get {
                return ResourceManager.GetString("Register here", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Register_now {
            get {
                return ResourceManager.GetString("Register now", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RegisterHeader1 {
            get {
                return ResourceManager.GetString("RegisterHeader1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RegisterHeader2 {
            get {
                return ResourceManager.GetString("RegisterHeader2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RegisterHeader3 {
            get {
                return ResourceManager.GetString("RegisterHeader3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RegisterHeader4 {
            get {
                return ResourceManager.GetString("RegisterHeader4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RegisterHeader5 {
            get {
                return ResourceManager.GetString("RegisterHeader5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Regular_Cleaning {
            get {
                return ResourceManager.GetString("Regular Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Remember_Me {
            get {
                return ResourceManager.GetString("Remember Me", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Request_List {
            get {
                return ResourceManager.GetString("Request List", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RequestControl {
            get {
                return ResourceManager.GetString("RequestControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RequiredAttribute_ValidationError {
            get {
                return ResourceManager.GetString("RequiredAttribute_ValidationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ResetDashboard {
            get {
                return ResourceManager.GetString("ResetDashboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ResetPassword {
            get {
                return ResourceManager.GetString("ResetPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ResetPasswordMessage {
            get {
                return ResourceManager.GetString("ResetPasswordMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ResetTitle {
            get {
                return ResourceManager.GetString("ResetTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Role {
            get {
                return ResourceManager.GetString("Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Roofer {
            get {
                return ResourceManager.GetString("Roofer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Room {
            get {
                return ResourceManager.GetString("Room", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RoomList {
            get {
                return ResourceManager.GetString("RoomList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Rooms {
            get {
                return ResourceManager.GetString("Rooms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Russian {
            get {
                return ResourceManager.GetString("Russian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Save_changes {
            get {
                return ResourceManager.GetString("Save changes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Select_an_activity {
            get {
                return ResourceManager.GetString("Select an activity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Select_Branch {
            get {
                return ResourceManager.GetString("Select Branch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Select_Template {
            get {
                return ResourceManager.GetString("Select Template", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Select_the_Activity_you_want {
            get {
                return ResourceManager.GetString("Select the Activity you want", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Select_the_cantons_in_which_you_would_like_to_work_here {
            get {
                return ResourceManager.GetString("Select the cantons in which you would like to work here", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Send {
            get {
                return ResourceManager.GetString("Send", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string SendEmail {
            get {
                return ResourceManager.GetString("SendEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string SendEmailTo {
            get {
                return ResourceManager.GetString("SendEmailTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string SendMessageToTheCustomer {
            get {
                return ResourceManager.GetString("SendMessageToTheCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string SendResetLink {
            get {
                return ResourceManager.GetString("SendResetLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Settings {
            get {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string SevenFloor {
            get {
                return ResourceManager.GetString("SevenFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Show_all_Activity {
            get {
                return ResourceManager.GetString("Show all Activity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Show_all_Archive_data {
            get {
                return ResourceManager.GetString("Show all Archive data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Show_all_Inventory {
            get {
                return ResourceManager.GetString("Show all Inventory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Show_all_Statistics {
            get {
                return ResourceManager.GetString("Show all Statistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Sightseeing {
            get {
                return ResourceManager.GetString("Sightseeing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string SixFloor {
            get {
                return ResourceManager.GetString("SixFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string SmallTransport {
            get {
                return ResourceManager.GetString("SmallTransport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string SoilType {
            get {
                return ResourceManager.GetString("SoilType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Sold {
            get {
                return ResourceManager.GetString("Sold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string SomeThingWentWrong {
            get {
                return ResourceManager.GetString("SomeThingWentWrong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Space {
            get {
                return ResourceManager.GetString("Space", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Spanish {
            get {
                return ResourceManager.GetString("Spanish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Start_Date {
            get {
                return ResourceManager.GetString("Start Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string StartDate {
            get {
                return ResourceManager.GetString("StartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Statistics {
            get {
                return ResourceManager.GetString("Statistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string StatisticsPageTitle {
            get {
                return ResourceManager.GetString("StatisticsPageTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string StatisticsPageTitlePartner {
            get {
                return ResourceManager.GetString("StatisticsPageTitlePartner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string StatisticsPageTitlePartner2 {
            get {
                return ResourceManager.GetString("StatisticsPageTitlePartner2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Storage {
            get {
                return ResourceManager.GetString("Storage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Street {
            get {
                return ResourceManager.GetString("Street", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string StringLengthAttribute_ValidationError {
            get {
                return ResourceManager.GetString("StringLengthAttribute_ValidationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Subject {
            get {
                return ResourceManager.GetString("Subject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string System_setup {
            get {
                return ResourceManager.GetString("System setup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Team {
            get {
                return ResourceManager.GetString("Team", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string TenFloor {
            get {
                return ResourceManager.GetString("TenFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string TermsNotAccepted {
            get {
                return ResourceManager.GetString("TermsNotAccepted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Thank_you_for_your_payment___ {
            get {
                return ResourceManager.GetString("Thank you for your payment...", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string The_achievements_of_partnere_for_the_current_month {
            get {
                return ResourceManager.GetString("The achievements of partnere for the current month", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string The_total__CHF {
            get {
                return ResourceManager.GetString("The total: CHF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string This_request_will_be_deleted_after_archiving_ {
            get {
                return ResourceManager.GetString("This request will be deleted after archiving!", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string This_table_displays_the_work_activity_statistics_for_the_current_year {
            get {
                return ResourceManager.GetString("This table displays the work activity statistics for the current year", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ThisIsRequiredRequest {
            get {
                return ResourceManager.GetString("ThisIsRequiredRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ThreeDays {
            get {
                return ResourceManager.GetString("ThreeDays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ThreeFloor {
            get {
                return ResourceManager.GetString("ThreeFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ThreeWeeks {
            get {
                return ResourceManager.GetString("ThreeWeeks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string To {
            get {
                return ResourceManager.GetString("To", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string To_which_branch {
            get {
                return ResourceManager.GetString("To which branch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Top_Up_Balance {
            get {
                return ResourceManager.GetString("Top Up Balance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Total_volume {
            get {
                return ResourceManager.GetString("Total volume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string toTheDrainEdge {
            get {
                return ResourceManager.GetString("toTheDrainEdge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string toTheLoadingEdge {
            get {
                return ResourceManager.GetString("toTheLoadingEdge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Turkish {
            get {
                return ResourceManager.GetString("Turkish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string TwoDays {
            get {
                return ResourceManager.GetString("TwoDays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string TwoFloor {
            get {
                return ResourceManager.GetString("TwoFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string TwoWeeks {
            get {
                return ResourceManager.GetString("TwoWeeks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string UID {
            get {
                return ResourceManager.GetString("UID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string UnauthorizedMessage {
            get {
                return ResourceManager.GetString("UnauthorizedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string UnauthorizedTitle {
            get {
                return ResourceManager.GetString("UnauthorizedTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Unit {
            get {
                return ResourceManager.GetString("Unit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Unpacking {
            get {
                return ResourceManager.GetString("Unpacking", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Update {
            get {
                return ResourceManager.GetString("Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string UpdateUser {
            get {
                return ResourceManager.GetString("UpdateUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Upload_File {
            get {
                return ResourceManager.GetString("Upload File", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Useless {
            get {
                return ResourceManager.GetString("Useless", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string UsersList {
            get {
                return ResourceManager.GetString("UsersList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string VerifyAccount {
            get {
                return ResourceManager.GetString("VerifyAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string View_All {
            get {
                return ResourceManager.GetString("View All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string View_order {
            get {
                return ResourceManager.GetString("View order", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Viewing {
            get {
                return ResourceManager.GetString("Viewing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ViewingDate {
            get {
                return ResourceManager.GetString("ViewingDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Volume__m3_ {
            get {
                return ResourceManager.GetString("Volume (m3)", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Volume_in_m3 {
            get {
                return ResourceManager.GetString("Volume in m3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Walls {
            get {
                return ResourceManager.GetString("Walls", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string WallsAndCeilings {
            get {
                return ResourceManager.GetString("WallsAndCeilings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Washroom {
            get {
                return ResourceManager.GetString("Washroom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Weak {
            get {
                return ResourceManager.GetString("Weak", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Website {
            get {
                return ResourceManager.GetString("Website", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string WelcomeBack {
            get {
                return ResourceManager.GetString("WelcomeBack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Welder {
            get {
                return ResourceManager.GetString("Welder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Window_Cleaning {
            get {
                return ResourceManager.GetString("Window Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Windows {
            get {
                return ResourceManager.GetString("Windows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Windows_Cleaning {
            get {
                return ResourceManager.GetString("Windows Cleaning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string WithInventoryList {
            get {
                return ResourceManager.GetString("WithInventoryList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Workers {
            get {
                return ResourceManager.GetString("Workers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Workspace {
            get {
                return ResourceManager.GetString("Workspace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string YesOn {
            get {
                return ResourceManager.GetString("YesOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string YouWantToPurchaseThisOrder {
            get {
                return ResourceManager.GetString("YouWantToPurchaseThisOrder", resourceCulture);
            }
        }
    }
}
